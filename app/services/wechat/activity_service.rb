class Wechat::ActivityService
  def self.async_all_record_from_origin!
    Serve::Origin.used.where(code: '公众号').find_each do |origin|
      next unless origin.mode # 没有设置slug不同步
      async_record!(origin: origin, submodule_id: 1, page: 1)
      sleep(10)
    end
  end

  def self.async_record!(origin: nil, submodule_id: 1, page: 1)
    response = RssApi::Wx.fetch_articles(page: page, slug: origin.mode)
    return unless response.present?
    Rails.logger.info "当前页数:#{page}"
    _hash = {}
    _hash[origin.mode] = page

    exclude_names = origin.payload&.dig('exclude_names') || ENV['WX_ARTICLE_EXCLUDE_NAMES'].to_s.split(',').presence || []
    include_names = origin.payload&.dig('include_names').to_s.split(',').presence || []

    save_page_numbers(_hash) if page > 1
    origin.update(latest_send_at: Time.now)
    response.fetch('data', [])&.each_with_index do |item, index|
      publish_at = Time.strptime(item['publish_time'], '%Y%m%d%H%M%S').localtime('+08:00')
      publish_at = publish_at.getutc

      Rails.logger.info "序号:#{index},标题：#{item['title']}"
      exclude_name = exclude_names.find { |n| item['title'].include?(n) }
      next if exclude_name

      include_name = include_names.find { |n| record['title'].include?(n) }
      next if include_names.present? && include_name.nil?

      content = Base64.decode64 item['content'] rescue next
      content = content.force_encoding('UTF-8')
      next if content.include?('服务器获取中')

      content = content.gsub('data-src="http', 'referrerpolicy="no-referrer" src="http')
      content = gsub_video_tag(content, item['original_url'])
      # 如果初始tag_name不存在，再查找内容中包含的tag_name
      # tag_name ||= CONT_NAMES.find { |n| content.include?(n) }
      # next unless tag_name.present?

      activity = Serve::Activity.find_or_initialize_by(
        name: item['title'],
        origin: origin,
        submodule_id: submodule_id,
      )
      # 根据设定好的关键字，增加相应的tag
      Rails.logger.info "保存activity,#{activity}"
      activity.update(
        effective_at: publish_at,
        state: 'pending',
        content: {
          content: [{ "key": item['publish_time'], "body": content }]
        },
        model_payload: {
          author: item['author'],
          link: item['original_url'],
          location: item['location']
        }
      )
    rescue Exception => e
      next
    end
  end

  def self.read_page_numbers
    file_path = Rails.root.join('tmp', 'wx_pages.json')
    if File.exist?(file_path)
      # 读取文件内容并将其解析为哈希对象
      json_data = File.read(file_path)
      JSON.parse(json_data, symbolize_names: true)
    else
      # 如果文件不存在，返回一个空的哈希对象作为默认值
      {}
    end
  end

  def self.save_page_numbers(pages_hash)
    # 读取现有的页码哈希对象
    existing_pages = read_page_numbers
    # 覆盖或添加新的键值对
    updated_pages = existing_pages.merge(pages_hash)
    # 将更新后的哈希对象转换为JSON字符串并保存
    json_data = updated_pages.to_json
    File.open(Rails.root.join('tmp', 'wx_pages.json'), 'w') do |file|
      file.write(json_data)
    end
  end

  def self.gsub_video_tag(html, source_url)
    # 解析 HTML 文本
    doc = Nokogiri::HTML(html)
    # 遍历所有的 video 标签
    # 定义一个通用方法来处理标签
    media_tags = ['iframe.video_iframe', 'video']
    media_tags.each do |tag|
      doc.css(tag).each do |media|
        next unless media.element?
        # 创建一个新的 a 标签，用于覆盖媒体标签
        a_tag = Nokogiri::HTML5::DocumentFragment.parse("<a href='#{source_url}' style='position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; z-index:10;'></a>")
        media_clone = media.clone
        # 将克隆的媒体标签添加到 a 标签中
        a_tag.at('a').add_child(media_clone)
        # 将 a 标签插入到媒体标签之前，并移除原来的媒体标签
        media.add_previous_sibling(a_tag)
        media.remove
      end
    end
    # 返回修改后的 HTML 文本
    doc.to_html
  end
end
