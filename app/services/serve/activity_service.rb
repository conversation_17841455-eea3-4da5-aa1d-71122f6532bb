class Serve::ActivityService
  class << self
    def settings
      []
    end

    def async_all_records!(submodule_id: 1)
      settings.each { |setting| async_record!(setting: setting, submodule_id: submodule_id) }
    end

    def async_all_records_from_first_page!(submodule_id: 1)
      options = settings.deep_dup
      options.each do |setting|
        setting[:list].delete(:page)
        if setting[:origin]
          origins = setting[:origin].to_s.split(',')
          origin = Serve::Origin.find_or_initialize_by(name: origins[0], code: origins[1] || '网站', app_id: app_id)
          next if origin.state != 'used'
        end
        async_record!(setting: setting, submodule_id: submodule_id)
      end
    end

    def async_record!(setting:, submodule_id: 1, app_id: 1)
      records = []
      # 获取页面内容
      docs = paginate(setting[:list])
      # 匹配数据
      docs.each do |doc|
        record = get_data_from_setting(doc, setting)
        records.push(record) if record
      end

      save_record!(records: records, setting: setting, submodule_id: submodule_id, app_id: app_id)
    end

    def save_record!(records:, setting:, submodule_id: 1, app_id: 1)
      tag_names = setting[:tag].to_s.split(',')
      tag_ids = []
      tag_names.each do |name|
        tag = Serve::Tag.find_or_create_by(name: setting[:tag], submodule_id: submodule_id)
        tag_ids.push(tag.id)
      end

      # 后续可以移出
      records.each_with_index do |record, index|
        puts "====#{index}==========", record
        # 包含会议的不在同步

        effective_at = record['published_at'].to_datetime rescue nil
        activity = Serve::Activity.find_or_initialize_by(
          name: record['title'],
          effective_at: effective_at,
          published_at: effective_at,
          submodule_id: submodule_id,
        )

        if url = record['content']['url'] && record['content'].is_a?(Hash)
          url = upload_file_to_oss(url) if setting[:upload]
          content = transform_video_to_content(url)
        else
          content = upload_file_from_content(record['content'])
          content = transform_html_to_content(content)
        end
        _tag_ids = activity.tag_ids + tag_ids
        m = record['model_payload'] || {}
        m[:catalog] = setting[:catalog] if setting[:catalog]

        origin_id = nil
        if setting[:origin]
          origins = setting[:origin].to_s.split(',')
          origin = Serve::Origin.find_or_initialize_by(name: origins[0], code: origins[1] || '网站', app_id: app_id)
          origin.update(latest_send_at: Time.now)
          # 过滤掉非必须同步的
          exclude_names = origin.payload&.dig('exclude_names').to_s.split(',').presence || ENV['WX_ARTICLE_EXCLUDE_NAMES'].to_s.split(',').presence || []
          exclude_name = exclude_names.find { |n| record['title'].include?(n) }
          next if exclude_name

          include_names = origin.payload&.dig('include_names') || []
          include_name = include_names.find { |n| record['title'].include?(n) }
          next if include_names.present? && include_name.nil?

          origin_id = origin.id
        end

        next unless origin_id

        activity.update!(
          model_flag: setting[:uid] || [setting[:name], setting[:catalog]] * '',
          content: content,
          model_payload: m,
          state: 'pending',
          origin_id: origin_id,
          tag_ids: _tag_ids.uniq
        )
      rescue Exception => e
        next
      end
    end

    # 根据配置获取数据 转换成activerecord
    def get_data_from_setting(doc, setting)
      # 获取详情页面的链接
      link_css = setting[:detail][:link_css]
      href = link_css ? doc.at_css(link_css)['href'] : doc['href']
      href = try(setting[:detail][:link_method], href) if setting[:detail][:link_method]
      url = get_link(setting[:list][:url], href)

      record = { 'model_payload' => { url: url, link_to_origin: setting[:disable_detail] ? 'true' : 'false' } }
      setting[:list][:attrs].each { |attr| get_record_attr(doc, attr, setting, record) }

      unless setting[:disable_detail]
      # 获取详情
        detail = setting[:detail].merge(url: url)
        detail_doc = setting[:detail][:driver] ? get_element_by_driver(detail) : get_element_by_uri(detail)
        detail_doc.css(detail[:exclude_css]).remove if detail[:exclude_css]

        if detail_doc
          setting[:detail][:attrs].each { |attr| get_record_attr(detail_doc, attr, detail, record) }
        end

        if iframe_setting = setting[:detail][:iframe]
          iframe = detail_doc.at_css('iframe')
          if iframe && iframe['src']
            s = iframe_setting.merge(url: iframe['src'])
            iframe_doc = s[:driver] ? get_element_by_driver(s) : get_element_by_uri(s)
            s[:attrs].each { |attr| get_record_attr(iframe_doc, attr, s, record) }
          end
        end
      end
      record.with_indifferent_access
    end

    # 根据详情页面直接抓取
    def get_data_from_setting_by_uri(url, setting)
      # 获取详情页面的链接
      record = { 'model_payload' => { url: url } }
      # 获取详情
      detail = setting[:detail].merge(url: url)
      detail_doc = setting[:detail][:driver] ? get_element_by_driver(detail) : get_element_by_uri(detail)
      detail_doc.css(detail[:exclude_css]).remove if detail[:exclude_css]

      if detail_doc
        setting[:detail][:attrs].each { |attr| get_record_attr(detail_doc, attr, detail, record) }
      end

      if iframe_setting = setting[:detail][:iframe]
        iframe = detail_doc.at_css('iframe')
        if iframe && iframe['src']
          s = iframe_setting.merge(url: iframe['src'])
          iframe_doc = s[:driver] ? get_element_by_driver(s) : get_element_by_uri(s)
          s[:attrs].each { |attr| get_record_attr(iframe_doc, attr, s, record) }
        end
      end
      record.with_indifferent_access
    end

    # 转化成activerecord的属性
    def transform_attribute(attr, data, record)
      # json字段
      if attr[:storage]
        record[attr[:storage]] ||= {}
        record[attr[:storage]][attr[:key]] = data.is_a?(String) ? data&.strip : data
      else
        record[attr[:key]] = data.is_a?(String) ? data&.strip : data
      end
      record
    end

    # 获取activerecord的属性
    def get_record_attr(doc, attr, setting, record)
      if attr[:method].present?
        data = try(attr[:method], doc)
      elsif attr[:video]
        data = get_video_url(doc, setting)
      else
        data = get_data_from_doc(doc, attr)
        data = transform_content_link(data, setting[:url]) if attr[:transform]
      end

      transform_attribute(attr, data, record)
    end

    # 匹配数据
    def get_data_from_doc(doc, attr)
      data = attr[:attr] ? doc[attr[:attr]] : doc.css(attr[:css])
      data = attr[:html] ? data.to_html : data.text if attr[:css]
      data
    end

    # 转化数据
    def transform_content_link(content, base_url, metas = ['img[src]', 'a[href]', 'video[src]'])
      doc, urls = Nokogiri::HTML(content), []
      metas.each do |css|
        doc.css(css).each do |ele|
          src = css == 'a[href]' ? ele['href'] : ele['src']
          next if src.blank? || src.start_with?('http') || src.in?(['.', '/']) || src.include?('javascript')
          urls.push(src) unless src.in?(urls)
        end
      end
      # 转换内容的链接
      urls.each { |url| content.gsub!(url, get_link(base_url, url)) }
      content
    end

    def get_link(base_url, url)
      return url if url.start_with?('http')
      uri = URI.parse(base_url)
      (uri + url).to_s
    end

    # 模拟浏览器
    def get_element_by_driver(setting)
      options = Selenium::WebDriver::Chrome::Options.new
      options.add_argument('--ignore-certificate-errors')
      options.add_argument('--headless') # 启用无头模式
      options.add_argument('--disable-gpu') # 禁用 GPU 加速
      options.add_argument('--no-sandbox') # 无沙箱模式
      options.add_argument('--disable-dev-shm-usage') # 共享内存禁用

      driver = Selenium::WebDriver.for :chrome, options: options
      driver.navigate.to setting[:url]

      wait = Selenium::WebDriver::Wait.new(timeout: 30) # 等待 10 秒
      wait.until { driver.find_element(css: setting[:css]) } if setting[:css]

      content = driver.page_source
      driver.quit

      Nokogiri::HTML content
    rescue Exception => e
      driver.quit
    end

    # 直接读取
    def get_element_by_uri(setting)
      element = Nokogiri::HTML(URI.open(setting[:url]))
      # 获取script中的html
      if setting[:script].present?
        content = element.css(setting[:script]).last.content
        gsubs = setting[:gsub] || []
        gsubs.each { |a| content = content.gsub(a.first, a.last) }
        element = Nokogiri::HTML(content)
      end
      element
    rescue Exception => e
      nil
    end

    # 分页
    def paginate(setting)
      docs = setting[:driver] ? paginate_driver(setting) : paginate_uri(setting)
      docs = docs.is_a?(Array) ? docs : [docs]
      _docs = []
      docs.each do |elements|
        elements.each do |ele|
          if setting[:filter]
            _docs.push(ele) if ele.css(setting[:filter]).present?
          else
            _docs.push(ele)
          end
        end
      end
      _docs
    end

    def paginate_uri(setting)
      docs = []

      if setting[:page].present?
        next_url = setting[:url]
        query = setting[:page][:query]
        next_url += "?#{query}=1" if query

        setting[:page][:total_page].times do |i|
          element = get_element_by_uri setting.merge(url: next_url)
          next unless element
          docs.push(element.css(setting[:css]))
          if setting[:page][:next_page_url]
            next_url = try(setting[:page][:next_page_url], setting, i + 1)
          elsif setting[:page][:css]
            href = element.at_css(setting[:page][:css])['href']
            next_url = get_link(next_url, href) if href.present?
          else
            page = i + 2
            next_url = setting[:url] + "?#{query}=#{page}"
          end
        end
      else
        element = get_element_by_uri setting
        docs.push(element.css(setting[:css]))
      end

      docs.compact
    end

    def paginate_driver(setting)
      options = Selenium::WebDriver::Chrome::Options.new
      options.add_argument('--ignore-certificate-errors')
      options.add_argument('--headless') # 启用无头模式
      options.add_argument('--disable-gpu') # 禁用 GPU 加速
      options.add_argument('--no-sandbox') # 无沙箱模式
      options.add_argument('--disable-dev-shm-usage') # 共享内存禁用

      driver = Selenium::WebDriver.for :chrome, options: options
      driver.navigate.to setting[:url]

      # 第一页
      wait = Selenium::WebDriver::Wait.new(timeout: setting[:wait_time] || 30) # 等待 10 秒
      wait.until { driver.find_element(css: setting[:css]) }

      docs = []
      docs.push(get_docs_form_driver(driver, setting))

      # 分页
      if setting[:page].present?
        (setting[:page][:total_page] - 1).times do |i|
          next_page = driver.find_element(css: setting[:page][:css])
          next_page.click
          wait.until { driver.find_element(css: setting[:css]) }
          docs.push(get_docs_form_driver(driver, setting))
        end
      end
      driver.quit
      docs
    rescue Exception => e
      driver.quit
    end

    def get_docs_form_driver(driver, setting)
      content = driver.page_source
      element = Nokogiri::HTML content
      if setting[:script].present?
        content = element.at_css(setting[:script]).content
        gsubs = setting[:gsub] || []
        gsubs.each { |a| content = content.gsub(a.first, a.last) }
      end
      element = Nokogiri::HTML content
      element.css(setting[:css])
    end

    def get_video_url(doc, setting)
      video = doc.at_css(setting[:video]&.dig(:css) || 'video')
      base_url = setting[:url] || setting[:base_url]

      return nil unless video

      {
        url: get_link(base_url, video['src']),
        bg_img_url: video['poster']
      }
    end

    def transform_html_to_content(body)
      {
        content: [
          {
            key: "#{Time.zone.now.strftime('%Y%m%d%H%M%S')}",
            body: body
          }
        ]
      }
    end

    def transform_video_to_content(url)
      {
        content: [
          {
            key: "#{Time.zone.now.strftime('%Y%m%d%H%M%S')}",
            videos: [
              {
                url: url,
                status: 'done',
                percent: 100,
                fileName: '视频.mp4',
                fileType: 'mp4',
                filename: '视频.mp4',
                mimeType: 'video/mp4',
                content_type: 'video/mp4',
                fileCategory: 'video',
                service_name: 's3'
              }
            ]
          }
        ]
      }
    end

    # 从内容中下载附件 主要是video img
    def upload_file_from_content(content)
      doc = Nokogiri::HTML content
      metas = ['img[src]']
      metas.each do |css|
        doc.css('img[src]').each do |img|
          src = img['src']
          next if src.nil? || src.include?('bjjw.hhtz.gov.cn')
          puts '=====src==', src
          filename = File.basename(URI.parse(src).path)
          url = upload_file_to_oss(src, filename: filename)
          content.gsub!(src, url)
        end
      end
      content
    end

    # 上传文件到oss
    def upload_file_to_oss(url, filename: nil)
      result = OssService.put(URI.open(url), filename: filename)
      result[:url] || url
    rescue Exception => e
      url
    end

    # activity的图片下载替换
    def async_all_serve_activity_file!
      Serve::Activity.find_each do |activity|
        async_serve_activity_file!(activity)
      end
    end

    def async_serve_activity_file!(activity)
      content = activity.content.dig('content') || []
      body = content.first
      if body && body.dig('body').present?
        doc = body['body']
        body['body'] = upload_file_from_content(doc)
        activity.save
      end
    rescue Exception => e
      false
    end
  end
end