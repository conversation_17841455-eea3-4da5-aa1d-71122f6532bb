class Serve::ZhejiangActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '宁波',
        catalog: '监督曝光',
        origin: '浙江省纪委监委网站',
        base_url: 'http://www.nbjw.gov.cn/',
        list: {
          url: 'http://www.nbjw.gov.cn/col/col1229602849/index.html?uid=7469158',
          script: 'script[type="text/xml"]',
          gsub: [[/<!\[CDATA\[|\]\]>/, '']],
          css: 'record a',
          page: {
            total_page: 15,
            query: 'pageNum'
          },
          attrs: [
            { key: 'title', css: 'h1' },
            { key: 'published_at', css: 'h3' }
          ]
        },
        detail: {
          attrs: [
            { key: 'content', css: 'div.J1-Article-content', html: true, transform: true },
            { key: 'origin', css: 'h3.J1-Article-header2', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '温州',
        catalog: '举案说纪法',
        origin: '浙江省纪委监委网站',
        base_url: 'https://wzlzw.gov.cn/',
        list: {
          css: 'record li a',
          url: 'https://wzlzw.gov.cn/col/col1229567740/index.html',
          script: 'script[type="text/xml"]',
          gsub: [[/<!\[CDATA\[|\]\]>/, '']],
          attrs: [
            { key: 'title', css: 'div.zmain' },
            { key: 'published_at', css: 'div.ytime' }
          ]
        },
        detail: {
          attrs: [
            { key: 'content', css: 'div.showmain', html: true, transform: true }
          ]
        }
      },
      {
        name: '杭州',
        catalog: '廉政经纬',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.hzlz.gov.cn/',
        list: {
          css: 'ul.list_items_detail li',
          url: 'https://www.hzlz.gov.cn/40',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.page-link:nth-of-type(1)'
          },
          attrs: [
            { key: 'title', css: 'a p' },
            { key: 'published_at', css: 'a span' }
          ]
        },
        detail: {
          link_css: 'a',
          driver: true,
          attrs: [
            { key: 'content', video: true, transform: true }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '清风之旅',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/zhuantizhuanlan/qinglianwenhua/qingfengzhilv/',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.pn'
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '清廉广角',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/zhuantizhuanlan/qinglianwenhua/shuhuasheying/',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.pn'
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '警示教育',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/jingshijiaoyu/huashuoweiji/',
          page: {
            total_page: 2,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '警示教育',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/jingshijiaoyu/fanfusanji/paiyingji/',
          page: {
            total_page: 2,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '警示教育',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/jingshijiaoyu/fanfusanji/dahuji/',
          page: {
            total_page: 2,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '警示教育',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/jingshijiaoyu/fanfusanji/liehuji/',
          page: {
            total_page: 1,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '警示教育',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/jingshijiaoyu/yianshuoze/',
          page: {
            total_page: 2,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '清廉广角',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'http://www.zjsjw.gov.cn/zhuantizhuanlan/qinglianwenhua/shuhuasheying/',
          page: {
            total_page: 2,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '清廉广角',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/zhuantizhuanlan/qinglianwenhua/qingfengzhilv/',
          page: {
            total_page: 2,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '浙江',
        catalog: '清廉广角',
        origin: '浙江省纪委监委网站',
        base_url: 'https://www.zjsjw.gov.cn/',
        list: {
          css: 'ul.listText li',
          url: 'https://www.zjsjw.gov.cn/zhuantizhuanlan/qinglianwenhua/jiaguijiaxun/',
          page: {
            total_page: 6,
            css: 'a.pn',
            next_page_url: :next_page_url
          },
          attrs: [
            { key: 'title', css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.infoCont', html: true, transform: true },
            { key: 'origin', css: 'div.info', storage: 'model_payload' }
          ]
        }
      }
    ]
  end

  def self.next_page_url(setting, page)
    num = setting[:page][:total_page] - page
    File.join(setting[:url], "index_#{num}.shtml")
  end
end
