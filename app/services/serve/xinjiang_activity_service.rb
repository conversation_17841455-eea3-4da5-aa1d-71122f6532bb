class Serve::XinjiangActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '新疆',
        catalog: '惩治“微腐败”',
        origin: '新疆纪委监委网站',
        base_url: 'https://www.xjjw.gov.cn/',
        list: {
          url: 'https://www.xjjw.gov.cn/list/467/1.html',
          css: 'ul.list-ul li',
          page: {
            total_page: 15,
            css: 'div.pagecode a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.show-content', html: true, transform: true },
            { key: 'origin', css: 'div.show-aboutinfo span.show-source', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '新疆',
        catalog: '通报曝光',
        origin: '新疆纪委监委网站',
        base_url: 'https://www.xjjw.gov.cn/',
        list: {
          url: 'https://www.xjjw.gov.cn/list/468/1.html',
          css: 'ul.list-ul li',
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.show-content', html: true, transform: true },
            { key: 'origin', css: 'div.show-aboutinfo span.show-source', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '新疆',
        catalog: '忏悔警示',
        origin: '新疆纪委监委网站',
        base_url: 'https://www.xjjw.gov.cn/',
        list: {
          url: 'https://www.xjjw.gov.cn/list/479/1.html',
          css: 'ul.list-ul li',
          page: {
            total_page: 15,
            css: 'div.pagecode a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true },
            { key: 'origin', css: 'div.show-aboutinfo span.show-source', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
