class Serve::NingxiaActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '宁夏',
        catalog: '侵害群众利益的不正之风和腐败问题',
        origin: '宁夏纪委监委网站',
        base_url: 'http://www.nxjjjc.gov.cn/',
        list: {
          url: 'http://www.nxjjjc.gov.cn/xbnxjw/xxgk/ssfjbgt/lyfbwt/',
          css: 'ul.news-list li',
          driver: true,
          page: {
            total_page: 15,
            css: 'ul.news-list font > a:nth-last-of-type(2)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          link_method: :handle_link,
          attrs: [
            { key: 'content', css: 'div.news_article', html: true, transform: true },
            { key: 'origin', css: 'div.bon > span:first-child', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '宁夏',
        catalog: '扶贫领域腐败和作风问题典型案例',
        origin: '宁夏纪委监委网站',
        base_url: 'http://www.nxjjjc.gov.cn/',
        list: {
          url: 'http://www.nxjjjc.gov.cn/xbnxjw/xxgk/ssfjbgt/fbzfwt/',
          css: 'ul.news-list li',
          driver: true,
          page: {
            total_page: 15,
            css: 'ul.news-list font > a:nth-last-of-type(2)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          link_method: :handle_link,
          attrs: [
            { key: 'content', css: 'div.news_article', html: true, transform: true },
            { key: 'origin', css: 'div.bon > span:first-child', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '宁夏',
        tag: '八项规定',
        catalog: '违反中央八项规定精神典型问题',
        origin: '宁夏纪委监委网站',
        base_url: 'http://www.nxjjjc.gov.cn/',
        list: {
          url: 'http://www.nxjjjc.gov.cn/xbnxjw/xxgk/ssfjbgt/jsdxwt/',
          css: 'ul.news-list li',
          driver: true,
          page: {
            total_page: 15,
            css: 'ul.news-list font > a:nth-last-of-type(2)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          link_method: :handle_link,
          attrs: [
            { key: 'content', css: 'div.news_article', html: true, transform: true },
            { key: 'origin', css: 'div.bon > span:first-child', storage: 'model_payload' }
          ]
        }
      }
    ]
  end

  def self.handle_link(href)
    href.gsub("\n", '').strip
  end
end
