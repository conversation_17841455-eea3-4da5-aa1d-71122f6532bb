class Serve::FujianActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '福建',
        tag: '八项规定',
        catalog: '违反中央八项规定问题',
        origin: '福建省纪委监委网站',
        base_url: 'https://www.fjcdi.gov.cn/',
        list: {
          url: 'https://www.fjcdi.gov.cn/cms/html/fjsjwjw/wfzybxgdjswt/index.html',
          css: 'div.news_list dl dd',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.list_foot dd > a:nth-of-type(3)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.wz-text', html: true, transform: true },
            { key: 'origin', css: 'div.summary > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '福建',
        catalog: '群众身边的腐败和作风问题',
        origin: '福建省纪委监委网站',
        base_url: 'https://www.fjcdi.gov.cn/',
        list: {
          url: 'https://www.fjcdi.gov.cn/cms/html/fjsjwjw/qzsbdfbhzfwt/index.html',
          css: 'div.news_list dl dd',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.list_foot dd > a:nth-of-type(3)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.wz-text', html: true, transform: true },
            { key: 'origin', css: 'div.summary > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '福建',
        catalog: '廉政漫画',
        origin: '福建省纪委监委网站',
        base_url: 'https://www.fjcdi.gov.cn/',
        list: {
          url: 'https://www.fjcdi.gov.cn/cms/html/fjsjwjw/lzmh/index.html',
          css: 'div.sp_list div',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.list_foot dd > a:nth-of-type(3)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.wz-text', html: true, transform: true },
            { key: 'origin', css: 'div.summary > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
