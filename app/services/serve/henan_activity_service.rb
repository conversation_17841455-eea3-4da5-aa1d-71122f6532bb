class Serve::HenanActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '河南',
        catalog: '通报曝光',
        origin: '河南省纪委监委网站',
        base_url: 'https://www.hnsjw.gov.cn/',
        list: {
          url: 'https://www.hnsjw.gov.cn/sitesources/hnsjct/page_pc/qwfbx/tbpg/list1.html',
          css: 'div#articleListTable div.info-box',
          filter: 'a',
          driver: true,
          page: {
            total_page: 15,
            css: "ul.pagination li a[aria-label='Next']"
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  method: :handle_published_at }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article-cont', html: true, transform: true },
            { key: 'origin', css: 'div.article-data > span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '河南',
        catalog: '纪法宣讲',
        origin: '河南省纪委监委网站',
        base_url: 'https://www.hnsjw.gov.cn/',
        list: {
          url: 'https://www.hnsjw.gov.cn/sitesources/hnsjct/page_pc/ljjy/jfxj/list1.html',
          css: 'div.list-fy-item',
          driver: true,
          page: {
            total_page: 2,
            css: "ul.pagination li a[aria-label='Next']"
          },
          attrs: [
            { key: 'title',  css: 'a div:nth-of-type(2)' }
          ]
        },
        detail: {
          link_css: 'a',
          driver: true,
          iframe: {
            css: 'video',
            driver: true,
            attrs: [
              { key: 'content', css: 'video', video: true }
            ]
          },
          attrs: [
            { key: 'published_at', css: 'div.article-data > span:nth-of-type(1)' },
            { key: 'origin', css: 'div.article-data > span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end

  def self.handle_published_at(doc)
    [
      doc.css('div.info-Date span:nth-of-type(2)').text,
      doc.css('div.info-Date span:nth-of-type(1)').text
    ] * '-'
  end
end
