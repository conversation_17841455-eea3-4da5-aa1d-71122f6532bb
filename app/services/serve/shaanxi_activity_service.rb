class Serve::ShaanxiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '陕西',
        catalog: '警示录',
        origin: '陕西省纪委监委网站',
        base_url: 'https://www.qinfeng.gov.cn/',
        list: {
          url: 'https://www.qinfeng.gov.cn/jsl.htm',
          css: 'ul.xsxc_index_center_list li',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.Next'
          },
          attrs: [
            { key: 'title',  css: 'a p' },
            { key: 'published_at',  css: 'p.time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_main', html: true, transform: true },
            { key: 'origin', css: 'div.article_date', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '陕西',
        catalog: '形式主义官僚主义典型问题',
        origin: '陕西省纪委监委网站',
        base_url: 'https://www.qinfeng.gov.cn/',
        list: {
          url: 'https://www.qinfeng.gov.cn/jdpg1/qb/xszyglzydxwt.htm',
          css: 'div.bgt_index_right ul li',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.Next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_main', html: true, transform: true },
            { key: 'origin', css: 'div.article_date', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '陕西',
        tag: '八项规定',
        catalog: '违反中央八项规定精神问题',
        origin: '陕西省纪委监委网站',
        base_url: 'https://www.qinfeng.gov.cn/',
        list: {
          url: 'https://www.qinfeng.gov.cn/jdpg1/qb/wfzybxgdjswt.htm',
          css: 'div.bgt_index_right ul li',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.Next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_main', html: true, transform: true },
            { key: 'origin', css: 'div.article_date', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
