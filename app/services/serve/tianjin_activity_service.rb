class Serve::TianjinActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '天津',
        catalog: '“四风”曝光台',
        origin: '天津市纪委监委网站',
        base_url: 'http://www.tjjw.gov.cn/',
        list: {
          url: 'https://www.tjjw.gov.cn/baoguangtai/list2_40009_1.html',
          css: 'ul#ListData li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span.l-time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article-content', html: true, transform: true },
            { key: 'origin', css: 'span.pc-time', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '天津',
        catalog: '亮剑“微腐败”/问责追责',
        origin: '天津市纪委监委网站',
        base_url: 'http://www.tjjw.gov.cn/',
        list: {
          url: 'https://www.tjjw.gov.cn/wfbhwz/list2_41994_1.html',
          css: 'ul#ListData li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span.l-time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article-content', html: true, transform: true },
            { key: 'origin', css: 'span.pc-time', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
