class Serve::GuangdongActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '广东',
        catalog: '曝光台',
        origin: '广东省纪委监委网站',
        base_url: 'https://www.gdjct.gd.gov.cn/',
        list: {
          url: 'https://www.gdjct.gd.gov.cn/SJW/index.html',
          css: 'ul.viewList li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'div.time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.zw-content', html: true, transform: true },
            { key: 'origin', css: 'div.info span:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
