class Serve::JiangsuActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '江苏',
        tag: '八项规定',
        origin: '江苏省纪委监委网站',
        catalog: '违反中央八项规定问题',
        base_url: 'https://www.jssjw.gov.cn/',
        list: {
          url: 'https://www.jssjw.gov.cn/col/col4170/index.html',
          css: 'div.default_pgContainer li',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.default_pgNext'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span font' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.c-conten-con', html: true, transform: true },
            { key: 'origin', css: 'div.c-conten-top > span:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '江苏',
        catalog: '群众身边的腐败和作风问题',
        origin: '江苏省纪委监委网站',
        base_url: 'https://www.jssjw.gov.cn/',
        list: {
          url: 'https://www.jssjw.gov.cn/col/col4171/index.html',
          css: 'div.default_pgContainer li',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.default_pgNext'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span font' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.c-conten-con', html: true, transform: true },
            { key: 'origin', css: 'div.c-conten-top > span:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
