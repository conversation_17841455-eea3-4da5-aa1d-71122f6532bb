class Serve::ChongqinActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '重庆',
        tag: '忏悔剖析',
        catalog: '忏悔剖析',
        origin: '重庆市纪委监委网站',
        base_url: 'http://jjc.cq.gov.cn/',
        list: {
          url: 'https://jjc.cq.gov.cn/html/col408013.htm',
          css: 'div#list ul:nth-of-type(-n+2) li',
          wait_time: 10,
          page: {
            total_page: 1,
            css: 'ul.pages li.page-next a'
          },
          attrs: [
            { key: 'title',  css: 'h1 a' },
            { key: 'published_at',  css: 'div.fright' },
            { key: 'summary', css: 'div:not(.fright)' }
          ]
        },
        detail: {
          link_css: 'h1 a',
          exclude_css: 'div.zfswbgsZ12463_ind04',
          attrs: [
            {
              key: 'content',
              css: 'body > div[align="center"]',
              html: true,
            }
          ]
        }
      },
      {
      name: '重庆',
      tag: '八项规定',
      catalog: '违反中央八项规定精神问题',
      origin: '重庆市纪委监委网站',
      base_url: 'http://jjc.cq.gov.cn/',
      list: {
        url: 'https://jjc.cq.gov.cn/html/col610113.htm',
        css: 'div#list ul:first-child li',
        driver: true,
        page: {
          total_page: 15,
          css: 'ul.pages li.page-next a'
        },
        attrs: [
          { key: 'title',  css: 'a' },
          { key: 'published_at',  css: 'div.fright' }
        ]
      },
      detail: {
        link_css: 'a',
        attrs: [
          { key: 'content', css: 'div.nr', html: true, transform: true },
          { key: 'origin', css: 'div.ls', storage: 'model_payload' }
        ]
      }
      }
    ]
  end
end
