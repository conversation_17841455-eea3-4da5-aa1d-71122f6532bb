class Serve::XinjiangbingtuanActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '新疆生产建设兵团',
        catalog: '监督曝光',
        origin: '新疆生产建设兵团纪委监委网站',
        base_url: 'http://btjw.xjbt.gov.cn/',
        list: {
          url: 'http://btjw.xjbt.gov.cn/xxgk/jdpg',
          css: 'div.list div.article',
          driver: true,
          attrs: [
            { key: 'title',  css: 'div.name' },
            { key: 'published_at',  css: 'div.time' }
          ]
        },
        detail: {
          link_css: 'a',
          driver: true,
          attrs: [
            { key: 'content', css: 'div.detail-con', html: true, transform: true },
            { key: 'origin', css: 'div.title_info span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
