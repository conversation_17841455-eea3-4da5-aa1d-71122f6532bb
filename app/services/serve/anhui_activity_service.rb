class Serve::AnhuiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '安徽',
        tag: '八项规定',
        catalog: '违反中央八项规定问题',
        origin: '安徽省纪委监委网站',
        base_url: 'http://www.ahjjjc.gov.cn/',
        list: {
          url: 'http://www.ahjjjc.gov.cn/wfzybxgdjswt300/index.html',
          css: 'div.item2_cont dl dd',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_content', html: true, transform: true },
            { key: 'origin', css: 'div.fl', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '安徽',
        catalog: '群众身边的腐败和作风问题',
        origin: '安徽省纪委监委网站',
        base_url: 'http://www.ahjjjc.gov.cn/',
        list: {
          url: 'http://www.ahjjjc.gov.cn/qhqzlydbzzfhfbwt301/index.html',
          css: 'div.item2_cont dl dd',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_content', html: true, transform: true },
            { key: 'origin', css: 'div.fl', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '安徽',
        catalog: '以案警示',
        origin: '安徽省纪委监委网站',
        base_url: 'http://www.ahjjjc.gov.cn/',
        list: {
          url: 'http://www.ahjjjc.gov.cn/yajs',
          css: 'div.news_row dl dd',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'p.time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_content', html: true, transform: true },
            { key: 'origin', css: 'div.fl', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '安徽',
        catalog: '忏悔录',
        origin: '安徽省纪委监委网站',
        base_url: 'http://www.ahjjjc.gov.cn/',
        list: {
          url: 'http://www.ahjjjc.gov.cn/hl248',
          css: 'div.news_row dl dd',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.next'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'p.time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article_content', html: true, transform: true },
            { key: 'origin', css: 'div.fl', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
