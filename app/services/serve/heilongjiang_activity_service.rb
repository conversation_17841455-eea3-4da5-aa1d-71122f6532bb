class Serve::HeilongjiangActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '黑龙江',
        catalog: '监督曝光',
        origin: '黑龙江省纪委监委网站',
        base_url: 'https://www.hljjjjc.gov.cn',
        list: {
          url: 'https://www.hljjjjc.gov.cn/Hljjjjc/html/news_list.html?first_classify_sign=qwfb&second_classify_sign=sfbgt',
          css: 'ul.Ul_block li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'div.time' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.detailContent', html: true, transform: true },
            { key: 'origin', css: 'div.source_by', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
