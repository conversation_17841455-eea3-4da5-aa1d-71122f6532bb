class Serve::YunnanActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '云南',
        catalog: '监督曝光',
        origin: '云南省纪委监委网站',
        base_url: 'http://www.ynjjjc.gov.cn/',
        list: {
          url: 'http://www.ynjjjc.gov.cn/html/jiandupuguang/',
          css: 'div.list_item_tit',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.pages a.a1'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true },
            { key: 'origin', css: 'div.fl', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
