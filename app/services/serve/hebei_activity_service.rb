class Serve::HebeiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '河北',
        tag: '八项规定',
        catalog: '违反中央八项规定精神问题',
        origin: '河北省纪委监委网站',
        base_url: 'http://www.hebcdi.gov.cn/',
        list: {
          url: 'http://www.hebcdi.gov.cn/node_137725.htm',
          css: 'div.min_aa',
          driver: true,
          page: {
            total_page: 15,
            css: 'li.page-next a'
          },
          attrs: [
            { key: 'title',  css: 'h2' },
            { key: 'published_at',  css: 'div.feed-time1' }
          ]
        },
        detail: {
          link_css: 'h2 a',
          attrs: [
            { key: 'content', css: 'div.min_content', html: true, transform: true },
            { key: 'origin', css: 'div.min_cc p span', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '河北',
        catalog: '群众身边腐败和作风问题',
        origin: '河北省纪委监委网站',
        base_url: 'http://www.hebcdi.gov.cn/',
        list: {
          url: 'http://www.hebcdi.gov.cn/node_137726.htm',
          css: 'div.min_aa',
          dtiver: true,
          page: {
            total_page: 15,
            css: 'li.page-next a'
          },
          attrs: [
            { key: 'title',  css: 'h2' },
            { key: 'published_at',  css: 'div.feed-time1' }
          ]
        },
        detail: {
          link_css: 'h2 a',
          attrs: [
            { key: 'content', css: 'div.min_content', html: true, transform: true },
            { key: 'origin', css: 'div.min_cc p span', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '河北',
        catalog: '以案说纪',
        origin: '河北省纪委监委网站',
        base_url: 'http://www.hebcdi.gov.cn/',
        list: {
          url: 'http://www.hebcdi.gov.cn/node_125484.htm',
          css: 'div.feed-item',
          dtiver: true,
          page: {
            total_page: 15,
            css: 'li.page-next a'
          },
          attrs: [
            { key: 'title',  css: 'h2' },
            { key: 'published_at',  css: 'div.feed-time' }
          ]
        },
        detail: {
          link_css: 'h2 a',
          attrs: [
            { key: 'content', css: 'div.min_content', html: true, transform: true },
            { key: 'origin', css: 'div.min_cc p span', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '河北',
        catalog: '漫说党纪',
        origin: '河北省纪委监委网站',
        base_url: 'http://www.hebcdi.gov.cn/',
        list: {
          url: 'http://www.hebcdi.gov.cn/node_356814.htm',
          css: 'div.min_bb',
          dtiver: true,
          page: {
            total_page: 15,
            css: 'li.page-next a'
          },
          attrs: [
            { key: 'title',  css: 'span a' }
          ]
        },
        detail: {
          link_css: 'span a',
          attrs: [
            { key: 'content', css: 'div.text', html: true, transform: true },
            { key: 'origin', css: 'div.source', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '河北',
        catalog: '以案警示',
        origin: '河北省纪委监委网站',
        base_url: 'http://www.hebcdi.gov.cn/',
        list: {
          url: 'http://www.hebcdi.gov.cn/node_122906.htm',
          css: 'div.feed-item',
          dtiver: true,
          page: {
            total_page: 15,
            css: 'li.page-next a'
          },
          attrs: [
            { key: 'title',  css: 'h2' },
            { key: 'published_at',  css: 'div.feed-time' }
          ]
        },
        detail: {
          link_css: 'h2 a',
          attrs: [
            { key: 'content', css: 'div.min_content', html: true, transform: true },
            { key: 'origin', css: 'div.min_cc p', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
