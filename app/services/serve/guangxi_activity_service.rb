class Serve::GuangxiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '广西',
        catalog: '监督曝光',
        origin: '广西省纪委监委网站',
        base_url: 'https://www.gxjjw.gov.cn/',
        list: {
          url: 'https://www.gxjjw.gov.cn/zt/baoguang/',
          css: 'ul.list_news_dl li dl dt',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true },
            { key: 'origin', css: 'div.daty span:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '广西',
        catalog: '案件警示',
        origin: '广西省纪委监委网站',
        base_url: 'https://www.gxjjw.gov.cn/',
        list: {
          url: 'https://www.gxjjw.gov.cn/staticmores/911/911-1.shtml',
          css: 'ul.m_textlist li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.more_page a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true },
            { key: 'origin', css: 'div.daty span:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
