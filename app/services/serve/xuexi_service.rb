class Serve::XuexiService < Serve::ActivityService
  def self.settings
    [
      {
        name: '学习强国',
        catalog: '学习时评',
        origin: '学习强国网站',
        base_url: 'https://www.xuexi.cn/',
        detail: {
          driver: true,
          css: 'div.render-detail-content',
          attrs: [
            { key: 'content', css: 'div.render-detail-content', html: true, transform: true },
            { key: 'origin', css: 'span.render-detail-resource', storage: 'model_payload' },
            { key: 'publish_at', css: 'span.render-detail-time' },
            { key: 'title', css: 'div.render-detail-titles' }
          ]
        }
      }
    ]
  end

  def self.async_record!(setting:, submodule_id: 1, app_id: 1)
  end
end
