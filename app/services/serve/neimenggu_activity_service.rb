class Serve::NeimengguActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '内蒙古',
        catalog: '案例剖析',
        origin: '内蒙古纪委监委网站',
        base_url: 'https://www.nmgjjjc.gov.cn/',
        list: {
          url: 'https://www.nmgjjjc.gov.cn/category/alpx.html?t=1723547434553',
          css: 'ul.c-list-post li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.c-content-con', html: true, transform: true },
            { key: 'origin', css: 'div.c-content-top i', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '内蒙古',
        catalog: '监督曝光',
        origin: '内蒙古纪委监委网站',
        base_url: 'https://www.nmgjjjc.gov.cn/',
        list: {
          url: 'https://www.nmgjjjc.gov.cn/category/dfzfjd.html?t=1723547433543',
          css: 'ul.c-list-post li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.c-content-con', html: true, transform: true },
            { key: 'origin', css: 'div.c-content-top i[data-name="文章来源"]', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
