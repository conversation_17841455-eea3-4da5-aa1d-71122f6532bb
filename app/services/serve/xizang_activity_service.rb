class Serve::XizangActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '西藏',
        catalog: '监督曝光',
        origin: '西藏纪委监委网站',
        base_url: 'http://www.xzjjw.gov.cn/',
        list: {
          url: 'http://www.xzjjw.gov.cn/jdpg.jhtml',
          css: 'ul.libiao li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true },
            { key: 'origin', css: 'div.art_info > div:first-child > div:first-child', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
