class Serve::JiangxiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '江西',
        catalog: '曝光台',
        origin: '江西省纪委监委网站',
        base_url: 'http://www.jxdi.gov.cn/',
        list: {
          url: 'http://www.jxdi.gov.cn/pgt/',
          css: 'td.red table tbody > tr:nth-of-type(2)',
          driver: true,
          page: {
            total_page: 15,
            css: 'a.STYLE1'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'td.title' }
          ]
        },
        detail: {
          link_css: 'a',
          base_url: 'http://www.jxdi.gov.cn/pgt/',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true },
            { key: 'origin', css: 'div.daty_con > em:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
