class Serve::ShanghaiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '上海',
        catalog: '群众身边的腐败和作风问题',
        origin: '上海市纪委监委网站',
        base_url: 'https://www.shjjjc.gov.cn/',
        list: {
          url: 'https://www.shjjjc.gov.cn/shsjjjcw/qzsbdfbhzfwt/qzsbdfbhzfwt.html',
          css: 'ul.m-list li',
          driver: true,
          page: {
            css: 'li.default_pgNext'
          },
          attrs: [
            { key: 'title',  css: 'p.biaoti' },
            { key: 'published_at',  css: 'p.riqi' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true }
          ]
        }
      },
      {
        name: '上海',
        tag: '八项规定',
        catalog: '违反中央八项规定问题',
        origin: '上海市纪委监委网站',
        base_url: 'https://www.shjjjc.gov.cn/',
        list: {
          url: 'https://www.shjjjc.gov.cn/shsjjjcw/wfzybxgdjswt/wfzybxgdjswt.html',
          css: 'ul.m-list li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'p.biaoti' },
            { key: 'published_at',  css: 'p.riqi' }
          ],
          page: {
            total_page: 10,
            next_page: 'api',
            func: ''
          }
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content', html: true, transform: true }
          ]
        }
      }
    ]
  end
end
