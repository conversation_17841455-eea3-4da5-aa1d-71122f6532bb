class Serve::ShandongActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '山东',
        catalog: '通报曝光',
        origin: '山东省纪委监委网站',
        base_url: 'https://www.sdjj.gov.cn/',
        list: {
          url: 'https://www.sdjj.gov.cn/tongbao/jiandu/',
          css: 'div.list ul li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div#page a.text'
          },
          attrs: [
            { key: 'title',  css: 'p' },
            { key: 'published_at',  css: 'span', method: 'published_at' }
          ]
        },
        detail: {
          link_css: 'a',
          base_url: 'https://www.sdjj.gov.cn/tongbao/jiandu/',
          attrs: [
            { key: 'content', css: 'div.TRS_Editor', html: true, transform: true },
            { key: 'origin', css: 'div.head p > span:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
