class Serve::GansuActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '甘肃',
        catalog: '监督曝光',
        origin: '甘肃省纪委监委网站',
        base_url: 'http://www.gsjw.gov.cn/',
        list: {
          url: 'http://www.gsjw.gov.cn/category/baoguangtai',
          css: 'ul.list li',
          filter: 'a',
          page: {
            total_page: 15,
            css: 'div.page > a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'em' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div#content', html: true, transform: true },
            { key: 'origin', css: 'div.content > div:nth-of-type(1) > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '甘肃',
        catalog: '纪法课堂',
        origin: '甘肃省纪委监委网站',
        base_url: 'http://www.gsjw.gov.cn/',
        list: {
          url: 'http://www.gsjw.gov.cn/category/ketang',
          css: 'ul.list li',
          filter: 'a',
          page: {
            total_page: 15,
            css: 'div.page > a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'em' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div#content', html: true, transform: true },
            { key: 'origin', css: 'div.content > div:nth-of-type(1) > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '甘肃',
        catalog: '纪法课堂',
        origin: '甘肃省纪委监委网站',
        base_url: 'http://www.gsjw.gov.cn/',
        list: {
          url: 'http://www.gsjw.gov.cn/category/jingjian',
          css: 'ul.list li',
          filter: 'a',
          page: {
            total_page: 15,
            css: 'div.page > a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'em' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div#content', html: true, transform: true },
            { key: 'origin', css: 'div.content > div:nth-of-type(1) > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '甘肃',
        catalog: '纪法课堂',
        origin: '甘肃省纪委监委网站',
        base_url: 'http://www.gsjw.gov.cn/',
        list: {
          url: 'http://www.gsjw.gov.cn/category/shiyi',
          css: 'ul.list li',
          filter: 'a',
          page: {
            total_page: 15,
            css: 'div.page > a:last-child'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'em' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div#content', html: true, transform: true },
            { key: 'origin', css: 'div.content > div:nth-of-type(1) > div:nth-of-type(1)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
