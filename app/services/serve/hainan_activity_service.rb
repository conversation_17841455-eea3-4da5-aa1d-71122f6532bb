class Serve::HainanActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '海南',
        catalog: '监督曝光',
        origin: '海南省纪委监委网站',
        base_url: 'https://www.hncdi.gov.cn/',
        list: {
          url: 'https://www.hncdi.gov.cn/web/hnlzw/v2/html/list.jsp?channelId=0b061e66-722b-4a9c-8abb-8d16b1b99b3c&channelCode=lzw_debk_jdbg_v2',
          css: 'p.lzw-info-list-item-info',
          driver: true,
          page: {
            total_page: 15,
            css: "div.page li[page-rel='nextpage']"
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at',  css: 'span.lzw-info-list-item-date' }
          ]
        },
        detail: {
          link_css: 'a',
          link_method: 'handle_link',
          driver: true,
          css: 'div.lzw-detail-content',
          attrs: [
            { key: 'content', css: 'div.lzw-detail-content', html: true, transform: true },
            { key: 'origin', css: 'div.lzw-detail-info span:nth-of-type(4)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end

  def self.handle_link(href)
    href.gsub("javascript:windowOpen('", '').gsub("')", '')
  end
end
