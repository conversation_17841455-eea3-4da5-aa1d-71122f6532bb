class Serve::QinghaiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '青海',
        catalog: '八项规定',
        origin: '青海省纪委监委网站',
        base_url: 'http://jjc.cq.gov.cn/',
        list: {
          url: 'http://www.qhjc.gov.cn/browse_29FEF3FB06F6035D_A1D538A83DCEE1DE.html',
          css: 'table table tr',
          driver: true,
          page: {
            total_page: 15,
            css: 'span#PageBar table td:nth-last-of-type(6)'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'td:nth-of-type(4)' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'span#ContentPlaceHolder1_docContent', html: true, transform: true },
            { key: 'origin', css: 'span#ContentPlaceHolder1_docFromPartion', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
