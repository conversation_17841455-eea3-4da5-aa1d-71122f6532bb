class Serve::GuizhouActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '贵州',
        catalog: '以案示警',
        base_url: 'http://www.gzdis.gov.cn/',
        origin: '贵州省纪委监委网站',
        list: {
          url: 'http://www.gzdis.gov.cn/lzjy/yajs/',
          css: 'div.NewsList ul li',
          driver: true,
          filter: 'a',
          page: {
            total_page: 15,
            css: 'div.page a.up'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          driver: true,
          video: {
            css: 'video'
          },
          attrs: [
            { key: 'content', video: true, transform: true },
            { key: 'origin', css: 'div.toolbar span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '贵州',
        tag: '八项规定',
        catalog: '违反中央八项规定精神问题',
        origin: '贵州省纪委监委网站',
        base_url: 'http://www.gzdis.gov.cn/',
        list: {
          url: 'http://www.gzdis.gov.cn/jdpg/qb/wfzybxgdjswt_5724770/',
          css: 'ul.list_news_dl li dt',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.up'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.trs_web', html: true, transform: true },
            { key: 'origin', css: 'div.toolbar span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '贵州',
        catalog: '群众身边的腐败和作风问题',
        origin: '贵州省纪委监委网站',
        base_url: 'http://www.gzdis.gov.cn/',
        list: {
          url: 'http://www.gzdis.gov.cn/jdpg/qb/qzsbdfbhzfwt_5724771/',
          css: 'ul.list_news_dl li dt',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.page a.up'
          },
          attrs: [
            { key: 'title',  css: 'a' },
            { key: 'published_at', css: 'span' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.trs_web', html: true, transform: true },
            { key: 'origin', css: 'div.toolbar span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
