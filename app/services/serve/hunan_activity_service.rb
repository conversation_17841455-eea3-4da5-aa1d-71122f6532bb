class Serve::HunanActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '湖南',
        catalog: '监督曝光',
        origin: '湖南省纪委监委网站',
        base_url: 'http://www.sxfj.gov.cn',
        list: {
          url: 'http://www.sxfj.gov.cn/jian_du_ju_bao/jian_du_bao_guang/index_1.shtml',
          css: 'div.catalog-block li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.content-list-page a.layui-laypage-next'
          },
          attrs: [
            { key: 'title',  css: 'span.catalog-content-text' },
            { key: 'published_at',  css: 'span.catalog-content-date' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content-detail-context', html: true, transform: true },
            { key: 'origin', css: 'div.content-detail-info span:nth-of-type(3)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '湖南',
        catalog: '漫说纪法',
        origin: '湖南省纪委监委网站',
        base_url: 'http://www.sxfj.gov.cn',
        list: {
          url: 'http://www.sxfj.gov.cn/jian_du_ju_bao/jian_du_bao_guang/index_1.shtml',
          css: 'div.catalog-block li',
          driver: true,
          attrs: [
            { key: 'title',  css: 'span.catalog-content-text' },
            { key: 'published_at',  css: 'span.catalog-content-date' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.content-detail-context', html: true, transform: true },
            { key: 'origin', css: 'div.content-detail-info span:nth-of-type(3)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '湖南',
        catalog: '镜鉴',
        origin: '湖南省纪委监委网站',
        base_url: 'http://www.sxfj.gov.cn',
        list: {
          url: 'http://www.sxfj.gov.cn/jing_jian/',
          css: 'div.catalog-block li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.content-list-page a.layui-laypage-next'
          },
          attrs: [
            { key: 'title',  css: 'span.catalog-content-text' },
            { key: 'published_at',  css: 'span.catalog-content-date' }
          ]
        },
        detail: {
          link_css: 'a',
          driver: true,
          video: {
            css: 'video'
          },
          attrs: [
            { key: 'content', css: 'div.content video', html: true, transform: true },
            { key: 'origin', css: 'div.content-detail-info span:nth-of-type(3)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
