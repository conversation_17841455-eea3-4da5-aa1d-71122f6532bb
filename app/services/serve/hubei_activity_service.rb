class Serve::HubeiActivityService < Serve::ActivityService
  def self.settings
    [
      {
        name: '湖北',
        catalog: '101个罪名解读',
        origin: '湖北省纪委监委网站',
        base_url: 'https://www.hbjwjc.gov.cn/',
        list: {
          url: 'https://www.hbjwjc.gov.cn/info/iList.jsp?cat_id=11713',
          css: 'ul.list-b li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.pages a.next'
          },
          attrs: [
            { key: 'title',  css: 'a h4' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article-box', html: true, transform: true },
            { key: 'published_at', css: 'div.info  span:nth-of-type(1)' },
            { key: 'origin', css: 'div.info  span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      },
      {
        name: '湖北',
        catalog: '典型通报',
        origin: '湖北省纪委监委网站',
        base_url: 'https://www.hbjwjc.gov.cn/',
        list: {
          url: 'https://www.hbjwjc.gov.cn/info/iList.jsp?cat_id=10008',
          css: 'ul.list-b li',
          driver: true,
          page: {
            total_page: 15,
            css: 'div.pages a.next'
          },
          attrs: [
            { key: 'title',  css: 'a h4' }
          ]
        },
        detail: {
          link_css: 'a',
          attrs: [
            { key: 'content', css: 'div.article-box', html: true, transform: true },
            { key: 'published_at', css: 'div.info  span:nth-of-type(1)' },
            { key: 'origin', css: 'div.info  span:nth-of-type(2)', storage: 'model_payload' }
          ]
        }
      }
    ]
  end
end
