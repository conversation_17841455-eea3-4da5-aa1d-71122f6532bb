class Iest::Intents::CreatePaperwork < ::Chat::Intent
  def prompt_summary
    super || '你是一个创建文档审核任务助手。请根据提供描述，提取文档审核任务所需信息。'
  end

  def keywords
    self.class.keywords
  end

  def self.keywords(content_type_tags: [], res_tags: [])
    [
      { key: 'file', type: :string, desc: '审查的文件' }
    ]
  end

  def need_mention?
    true
  end

  def answer(sentence)
    res = current_message.request_ai_response(prompt)
    generate_answer(res)
  end

  def present_or(a, b)
    a.present? ? a : b
  end

  def ai_response_to_payload(res)
    file_item = res.dig('file') || {}

    {
      attachment: {
        files: [file_item]
      }
      # user: current_message.user,
    }.with_indifferent_access
  end

  def generate_answer(res)
    # result = ai_response_to_payload(res)

    logger.info(res)
    if res.dig('file').blank? || !res.dig('file').is_a?(Hash)
      return {
        ai_response: res,
        result_type: 'create_paperwork_file_empty',
        content: '请上传需要的文件哦',
        mention_type: 'Iest::Mentions::Paperwork',
        mentionable_type: 'Iest::Paperwork'
      }

    elsif !(res.dig('file', 'url').end_with?('docx') || res.dig('file', 'url').end_with?('pdf'))
      return {
        ai_response: res,
        result_type: 'create_paperwork_file_type_error',
        content: '请上传 doc 或 docx 格式的文件',
        mention_type: 'Iest::Mentions::Paperwork',
        mentionable_type: 'Iest::Paperwork'
      }
    end

    {
      ai_response: res,
      result_type: 'create_serve_pack_done',
      content: '审查任务已创建，请稍等片刻~',
      mention_type: 'Iest::Mentions::Paperwork',
      mentionable_type: 'Iest::Paperwork',
      mention_attributes: {
        aasm_state_event_later: 'finish'
      }
    }
  end
end
