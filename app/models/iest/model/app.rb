module Iest::Model::App
  extend ActiveSupport::Concern

  included do
    has_many :serve_rule_groups,  class_name: 'Serve::RuleGroup', dependent: :destroy
    has_many :serve_rules, -> { current_tanent }, class_name: 'Serve::Rule',      dependent: :destroy
    has_many :serve_packs, -> { current_tanent }, class_name: 'Serve::Pack',      dependent: :destroy
    has_many :serve_origins,      class_name: 'Serve::Origin',    dependent: :destroy
    has_many :serve_messages, -> { current_tanent }, class_name: 'Serve::Message',   dependent: :destroy
    has_many :serve_content_type_tags, class_name: 'Serve::ContentTypeTag', dependent: :destroy
    has_many :serve_groups, class_name: 'Serve::Group'
    has_many :serve_rule_items, class_name: 'Serve::RuleItem',      dependent: :destroy

    has_many :iest_ai_chat_conversations, -> { current_tanent }, class_name: 'Iest::Ai::Chat::Conversation', dependent: :destroy
    has_many :iest_ai_chat_intents, -> { current_tanent }, class_name: 'Iest::Ai::Chat::Intent', dependent: :destroy

    has_many :iest_paperworks, class_name: 'Iest::Paperwork', dependent: :destroy

    has_many :serve_ai_packs, -> { current_tanent }, class_name: 'Serve::Pack',      dependent: :destroy
    has_many :serve_ai_messages, class_name: 'Serve::AiMessage', dependent: :destroy
    has_many :serve_ai_message_squares, class_name: 'Serve::AiMessageSquare', dependent: :destroy
    has_many :serve_ai_message_templates, class_name: 'Serve::AiMessageTemplate', dependent: :destroy
  end
end
