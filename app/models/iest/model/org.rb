module Iest::Model::Org
  extend ActiveSupport::Concern

  included do
    include Region::Ext::AreaSource
    acts_as_area(level: 'area')

    has_many :serve_packs, -> { current_tanent }, class_name: 'Serve::Pack', dependent: :nullify
    has_many :serve_origins, class_name: 'Serve::Origin', dependent: :nullify
    has_many :serve_messages, -> { current_tanent }, through: :serve_packs, source: :messages, class_name: 'Serve::Message'
    has_many :serve_ai_message_squares, class_name: 'Serve::AiMessageSquare'
    # has_many :des_serve_packs, -> { unscope(:order) }, through: :self_and_descendants, source: :serve_packs

    def des_serve_packs
      descendant_ids = self_and_descendants.pluck(:id)
      Serve::Pack.where(org_id: descendant_ids)
    end

    scope :screen_bid_pack, ->(name = '') {
      orgs = ransack(parent_name_eq: name).result
      orgs.each_with_object([]) do |org, arr|
        arr.push({
          name: org.name,
          des_packs_count: org.des_serve_packs.count
        })
        arr
      end
    }

    action_store(
      :relate,
      :department,
      class_name: '::Department',
      action_class_name: 'Serve::OrgAction',
      alias_name: 'inspect_departments',
      inverse_alias_name: 'inspect_orgs',
    )

    action_store_by(
      :relate,
      :org,
      class_name: 'Org',
      action_class_name: 'Serve::TanentAction',
      alias_name: 'orgs',
      inverse_alias_name: 'tanents',
    )

    def messages_count
      serve_messages.count
    end

    def unread_messages_count
      serve_messages.where(is_read: [nil, false]).count
    end

    def packs_count
      serve_packs.count
    end

    def rules_count
      rules.count
    end

    def users_count
      users.count
    end

    def serve_ai_message_squares_count
      serve_ai_message_squares.count
    end

    action_store_by(
      :relate,
      :org,
      class_name: 'Serve::Rule',
      action_class_name: 'Serve::OrgAction',
      alias_name: 'orgs',
      inverse_alias_name: 'rules',
    )

    def self.extra_permitted_attributes
      [
        :province,
        :city,
        :district,
        :region_area_id
      ]
    end

    def self.extra_attributes
      [
        :province,
        :city,
        :district,
        :region_area_id,
        :messages_count,
        :unread_messages_count,
        :rules_count,
        :packs_count,
        :users_count
      ]
    end

    def self.init_by_org_identity(org_identity_name:, app: App.first)
      org_identity = app.org_identities.find_by!(name: org_identity_name)
      Region::Province.all.each do |province|
        province_org = app.orgs.where(name: "#{org_identity_name} #{province.name}").first_or_create do |org|
          org.region_area = province
          org.org_identity = org_identity
        end

        province.children.each do |city|
          city_org = province_org.children.where(name: "#{org_identity_name} #{city.name}").first_or_create do |org|
            org.region_area = city
          end

          city.children.each do |district|
            district_org = city_org.children.where(name: "#{org_identity_name} #{district.name}").first_or_create do |org|
              org.region_area = district
            end
          end
        end
      end
    end
  end
end
