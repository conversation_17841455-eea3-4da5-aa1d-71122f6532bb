module Iest::Model::Res::Tag
  extend ActiveSupport::Concern

  included do
    include Res::Model::Tag

    scope :screen_order_user, ->(limit = 5) {
      tags = joins(:users).group('res_tags.id').limit(limit).order('count(users.id) DESC')
      result = []
      tags.each_with_index do |tag, index|
        result.push({
          name: tag.name,
          users_count: tag.users.count,
          sort: index + 1
        })
      end
      result
    }

    def users_count
      users.count
    end

    def self.extra_permitted_attributes
      [
        :users_count
      ]
    end
  end
end
