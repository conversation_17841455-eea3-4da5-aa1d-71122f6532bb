module Iest::Model::<PERSON><PERSON>
  extend ActiveSupport::Concern

  included do
    has_many :iest_ai_chat_conversations, class_name: 'Iest::Ai::Chat::Conversation', dependent: :destroy
    has_many :iest_ai_chat_intents, class_name: 'Iest::Ai::Chat::Intent', dependent: :destroy

    # 只需关联上级的 org
    action_store(
      :relate,
      :org,
      class_name: 'Org',
      action_class_name: 'Serve::TanentAction',
      alias_name: 'relate_orgs',
      inverse_alias_name: 'tanents',
    )

    action_store_by(
      :relate,
      :tanent,
      class_name: 'Tanent',
      action_class_name: 'Serve::TanentAction',
      alias_name: 'tanents',
      inverse_alias_name: 'serve_rules',
    )

    def orgs
      relate_orgs.present? ?
        app.orgs.ransack(self_and_ancestors_id_in: relate_orgs.pluck(:id)).result.distinct :
        app.orgs.none
    end

    def users
      relate_orgs.present? ?
        app.users.ransack(orgs_self_and_ancestors_id_in: relate_orgs.pluck(:id)).result.distinct :
        app.users.none
    end

    # action_store(
    #   :relate,
    #   :user,
    #   class_name: 'User',
    #   action_class_name: 'Serve::TanentAction',
    #   alias_name: 'users',
    #   inverse_alias_name: 'tanents',
    # )
  end
end
