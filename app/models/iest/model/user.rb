module Iest::Model::User
  extend ActiveSupport::Concern

  included do
    include Res::Model::User

    has_many :des_orgs, through: :orgs, source: :self_and_descendants

    has_many :serve_rules, through: :des_orgs, source: :rules
    has_many :serve_packs, through: :des_orgs, source: :serve_packs
    has_many :serve_origins, through: :des_orgs, source: :serve_origins
    has_many :serve_messages, class_name: 'Serve::Message'
    has_many :iest_ai_chat_conversations, class_name: 'Iest::Ai::Chat::Conversation', dependent: :nullify
    has_many :serve_rule_activities, through: :serve_messages, source: :activity

    has_many :serve_ai_messages, class_name: 'Serve::AiMessage', foreign_key: :creator_id
    has_many :serve_ai_message_templates, class_name: 'Serve::AiMessageTemplate', foreign_key: :creator_id
    has_many :serve_ai_message_squares,   class_name: 'Serve::AiMessageSquare',   foreign_key: :creator_id
    has_many :serve_receivers,   class_name: 'Serve::Receiver'
    has_many :serve_ai_packs,    class_name: 'Serve::Pack', foreign_key: :creator_id

    # 新人入职提醒
    scope :serve_rule_create_at_range, ->(rule_record, period = nil, unit = 'month') {
      # 发送时间
      prev_rule_record = rule_record.prev
      send_at = rule_record.schedule_offset_at
      last_send_at = prev_rule_record ? prev_rule_record.schedule_offset_at : period.to_i.try(unit).ago(send_at)
      ransack(created_at_gteq: last_send_at, created_at_lt: send_at).result
    }

    def unread_messages_count
      serve_messages.where(is_read: [nil, false]).count
    end

    def total_messages_count
      serve_messages.count
    end

    def read_messages_count
      serve_messages.where(is_read: true).count
    end

    def duty_name
      duty_names.first.to_s
    end

    def self.extra_view_attributes(mode)
      [
        :unread_messages_count,
        :read_messages_count,
        :total_messages_count,
        :res_tags
      ]
    end
  end
end
