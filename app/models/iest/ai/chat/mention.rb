class Iest::Ai::Chat::Mention < ApplicationRecord
  self.track_migration = true

  broadcasts

  stiable

  belongs_to :mentionable, polymorphic: true, optional: true
  belongs_to :conversation, class_name: 'Iest::Ai::Chat::Conversation'
  belongs_to :app, class_name: '::App'
  belongs_to :creator_intent, class_name: 'Iest::Ai::Chat::Intent', optional: true

  has_many :mention_versions, class_name: 'Iest::Ai::Chat::MentionVersion', dependent: :destroy
  has_many :messages, -> { distinct }, through: :mention_versions

  attribute :name, :string, comment: '名称'
  attribute :state, :string, comment: '状态'
  attribute :payload, :jsonb, comment: '数据'
  attribute :self_intent, :string, comment: '(弃用)意图标识，用于归集相同意图的消息下的 mentions'

  attr_accessor :relate_message_id

  default_value_for(:app) { |o| o.conversation&.app }

  before_save :set_name

  enum state: {
    pending: 'pending',
    done: 'done',
    terminated: 'terminated'
  }

  include AASM

  aasm column: :state, enum: true do
    state :pending, initial: true
    state :done
    state :terminated

    event :finish do
      transitions from: [:pending, :terminated], to: :done, after: :create_mentionable
    end

    event :terminate do
      transitions from: :pending, to: :terminated
    end
  end

  attr_accessor :aasm_state_event_later, :current_user

  after_save :trigger_aasm_state_event, if: :aasm_state_event_later

  def trigger_aasm_state_event
    event = aasm_state_event_later
    self.aasm_state_event_later = nil
    self.update!(aasm_state_event: event)
  end

  after_save :create_mention_version, if: -> {
    saved_change_to_payload?
  }

  def create_mentionable
    return unless mentionable_type.present? && mentionable.nil? && payload.present? &&

    mentionable_klass = mentionable_type.safe_constantize
    if mentionable_klass.present?
      new_mentionable = mentionable_klass.ta_create(
        attributes: payload,
        extra: { app: app, creator: current_user, tanent: Tanent.current }
      )
      update!(mentionable: new_mentionable)
    end
  end

  def create_mention_version
    if pending?
      mention_versions.create!(payload: payload, message_id: relate_message_id)
    end
  end

  def renew_version=(formData); end

  def set_name
    self.name = "#{creator_intent&.name}" if name.blank?
  end
end
