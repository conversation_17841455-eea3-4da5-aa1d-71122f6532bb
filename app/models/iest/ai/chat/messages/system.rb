class Iest::Ai::Chat::Messages::System < Iest::Ai::Chat::Message
  after_create :fill_result

  def fill_result
    exists_mention = conversation.mentions.find_by(state: 'pending')
    exists_intent = exists_mention&.creator_intent

    intent = get_intent(exists_intent) do |intent_scope|
      intent_scope.request_intent(
        previous_content,
        conversation: conversation,
      )
    end

    intent.current_message = self

    need_mention = intent.need_mention?

    if conversation.name.blank?
      conversation.update!(name: intent.request_conversation_name(previous_content))
    end

    if need_mention
      # 未识别到意图时，不创建 mention
      # 已识别到意图，创建 mention
      # 切换意图后，finish 之前的 mention
      intent.current_mention = conversation.mentions.find_or_create_by!(state: 'pending')
      if intent.current_mention.present? &&
        intent.current_mention.creator_intent.present? &&
        intent.current_mention.creator_intent != intent &&
        !intent.current_mention.creator_intent.self_and_descendants.include?(intent)

        intent.current_mention.terminate!
        intent.current_mention = conversation.mentions.find_or_create_by!(state: 'pending')
      end
      intent.current_mention.creator_intent ||= intent
      intent.current_mention.save!
    end

    answer = intent.answer(previous_content).with_indifferent_access

    update_columns(
      content: answer[:content],
      result_type: answer[:result_type],
      suggestions: answer[:suggestions],
    )

    # NOTE: 注意这里 子意图的搜索用户获得的 mention_attributes，与 root 意图搜索用户，结构不一样
    if need_mention
      intent.current_mention.mentionable_type ||= answer[:mentionable_type]
      intent.current_mention.type ||= answer[:mention_type]

      intent.current_mention.update!(
        payload: ai_response_to_payload(intent, answer[:ai_response]),
        ai_response: answer[:ai_response],
        relate_message_id: id,
      )
    end
  end

  def get_intent(current_intent = nil, &block)
    if current_intent.present?
      intent_scope = current_intent.self_and_descendants
      intent = yield(intent_scope)

      if intent.new_record?
        # 未找到匹配的新意图
        # 向上查找，直到找到一个可识别的意图
        return get_intent(intent.parent, &block)
      end

      return intent
    end

    intent_scope = conversation.possible_intents.roots
    intent = yield(intent_scope)

    intent
  end

  def ai_response_to_payload(intent, res)
    # NOTE: 这里和 answer 方法各运行了一次 ai_response_to_payload，能优化吗？
    # root_intent = intent
    # while root_intent.parent.present?
    #   root_intent = root_intent.parent
    # end
    # logger.info "root_intent: #{root_intent.inspect}"
    # root_intent.ai_response_to_payload(res)
    root_intent = intent.root || intent
    root_intent.current_mention = intent.current_mention
    root_intent.current_message = intent.current_message
    root_intent.ai_response_to_payload(res)
  end
end
