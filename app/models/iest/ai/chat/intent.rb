class Iest::Ai::Chat::Intent < ApplicationRecord
  self.track_migration = true

  stiable
  closure_treeable order: 'position'

  attribute :name, :string, comment: '意图名称'
  attribute :desc, :string, comment: '意图描述'
  attribute :prompt_summary, :string, comment: '提示词开场白'
  attribute :position, :integer, comment: '排序'

  validates_uniqueness_of :name, scope: :app_id

  attr_accessor :current_mention, :current_message

  belongs_to :app
  belongs_to :tanent, optional: true

  has_many :intent_keywords, class_name: 'Iest::Ai::Chat::IntentKeyword', dependent: :destroy

  scope :current_tanent, -> { Tanent.current ? where(tanent: Tanent.current) : all }

  def to_keywords(**opts)
    descendants.flat_map { |sub_intent| sub_intent.keywords(**opts) } + intent_keywords.to_keywords(**opts)
  end

  # 提供复写
  def keywords
    to_keywords
  end

  def prompt
    k = keywords
    <<-PROMPT
      **#{prompt_summary}**
      json 返回，格式为 #{self.class.keywords_to_format(k)}

      #{
        self.class.keywords_to_prompt(k)
      }
    PROMPT
  end

  # 提供复写
  def answer(sentence)
    generate_answer(res: {})
  end

  # 提供复写
  def need_mention?
    false
  end

  # 提供复写，由 AI 返回结果，生成 mention 需要数据结构
  def ai_response_to_payload(res)
    {}
  end

  def generate_answer(res)
    {
      ai_response: res,
      result_type: 'other',
      content: '我不明白您的意思，请重新描述。',
      mention_attributes: {}
    }
  end

  def request_conversation_name(content)
    default_name = '未命名对话'

    return default_name if new_record?

    prompt = <<-PROMPT
      **你是一个对话取名助手。用户将提供对话开场白，请为对话起一个概括标题，没有实际内容时返回"未命名"，字数不多于20字**
      json格式返回，格式为 { "name": "" }
    PROMPT

    res = current_message.request_ai_response(prompt, sentence: content)
    res.dig('name') || default_name
  end

  class << self
    def request_intent(sentence, conversation: nil)
      body = ::Chat::OpenaiService.completions({
        model: 'gpt-4-turbo',
        messages: [
          { role: 'system', content: prompt },
          *(conversation&.history_data || []),
          { role: 'user', content: sentence }
        ],
        response_format: { type: 'json_object' }
      })

      response = JSON.parse(body)
      intent = response.dig('intent')

      intent_selections_map[intent] || Iest::Ai::Chat::Intent.new
    end

    def prompt
      <<-PROMPT
        **你是一个意图识别助手。请根据提供对话，判断用户的下一步意图。**
        json 返回，格式为 { "intent": "" }
        #### `intent` 可选值为：
        #{intent_selections.join("\n")}
      PROMPT
    end

    def intent_selections_map
      all.map do |intent|
        [intent.name, intent]
      end.to_h
    end

    def intent_selections
      all.each_with_index.map do |intent, index|
        "#{index + 1}. `#{intent.name}`: #{intent.desc}"
      end.push(
        '0. `other`: 其他'
      )
    end

    def keywords_to_prompt(keywords)
      keywords.map do |keyword|
        "#### `#{keyword[:key]}` #{keyword[:desc]}"
      end.join("\n")
    end

    def keywords_to_format(keywords)
      "{#{
        keywords.map do |keyword|
          "\"#{keyword[:key]}\": #{keyword[:type] == :array ? '[]' : '""'}"
        end.join("\n")}
      }"
    end
  end
end
