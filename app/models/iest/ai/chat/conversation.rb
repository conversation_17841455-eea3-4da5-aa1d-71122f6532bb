class Iest::Ai::Chat::Conversation < ApplicationRecord
  self.track_migration = true

  broadcasts

  belongs_to :app
  belongs_to :tanent, optional: true
  belongs_to :user, class_name: '::User', optional: true

  # 对话目前意图
  belongs_to :current_intent, class_name: 'Iest::Ai::Chat::Intent', optional: true

  has_many :messages, class_name: 'Iest::Ai::Chat::Message', dependent: :destroy
  has_many :mentions, class_name: 'Iest::Ai::Chat::Mention', dependent: :nullify
  has_many :mention_versions, class_name: 'Iest::Ai::Chat::MentionVersion', dependent: :nullify

  attribute :name, :string, comment: '对话名称'

  delegate :name, to: :current_intent, prefix: true, allow_nil: true

  default_value_for(:app) { |o| o.user&.app }

  scope :current_tanent, -> { Tanent.current ? where(tanent: Tanent.current) : all }

  def possible_intents
    # app.iest_ai_chat_intents
    Tanent.current.iest_ai_chat_intents
  end

  def history_messages
    messages.order(created_at: :asc)
  end

  # 用在 prompt 的 历史对话中
  def history_data
    history_messages[0...-1].map do |message|
      {
        role: message.type === 'Iest::Ai::Chat::Messages::User' ? 'user' : 'assistant',
        content: message.content
      }
    end
  end
end
