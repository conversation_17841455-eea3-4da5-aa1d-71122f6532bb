class Iest::Ai::Chat::Mentions::ServePack < ::Chat::Mention
  # 换一篇文章
  def renew_version=(formdata)
    return unless formdata

    offset = formdata.delete('offset').to_i

    new_ids = Iest::Ai::Chat::Intents::SearchServeActivity.get_articles(
      article_content: payload.dig('payload', 'article_content'),
      serve_content_type_tag_ids: payload.dig('payload', 'serve_content_type_tag_ids'),
      app: app,
    ).offset(offset).limit(5).map(&:id)

    self.payload ||= {}
    self.payload['payload'] ||= {}
    self.payload['payload']['serve_activity_ids'] = new_ids
    self.relate_message_id = mention_versions.last&.message_id

    save!
  end
end
