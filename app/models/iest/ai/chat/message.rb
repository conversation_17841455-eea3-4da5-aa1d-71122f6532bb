class Iest::Ai::Chat::Message < ApplicationRecord
  self.track_migration = true

  stiable
  belongs_to :conversation, class_name: 'Iest::Ai::Chat::Conversation'

  has_many :mention_versions, class_name: 'Iest::Ai::Chat::MentionVersion', dependent: :destroy
  has_many :mentions, -> { distinct }, through: :mention_versions

  attribute :previous_content, :text, comment: '前一句话的内容'
  attribute :content, :text, comment: '内容'
  attribute :result_type, :string, comment: '处理结果标识'
  # attribute :meta, :jsonb, comment: '返回前端更新上下文的数据'

  class Suggestion
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :name, :string
    attr_json :action_type, :string
    attr_json :color, :string
  end

  class Suggestions
    include AttrJson::Model
    attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

    attr_json :suggestions, Suggestion.to_type, array: true, default: []
  end

  attribute :suggestions, :jsonb, coder: Suggestions.to_serialization_coder, comment: '提示建议'
end
