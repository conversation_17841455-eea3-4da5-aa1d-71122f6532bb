class Iest::Ai::Chat::MentionVersion < ApplicationRecord
  self.track_migration = true

  belongs_to :mention, class_name: 'Iest::Ai::Chat::Mention'
  belongs_to :message, class_name: 'Iest::Ai::Chat::Message'
  belongs_to :conversation, class_name: 'Iest::Ai::Chat::Conversation'

  # attribute :position, :integer, comment: '排序'
  attribute :payload, :jsonb, comment: '数据'

  default_value_for(:conversation) { |o| o.message&.conversation }

  # acts_as_list(scope: :mention_id)
end
