class Iest::Ai::Chat::Intents::SearchServeActivity < ::Chat::Intent
  def prompt_summary
    super || '你是一个搜索文章助手。请根据提供描述，提取搜索文章所需关键字。'
  end

  def keywords
    intent_keywords.present? ?
      to_keywords(
        content_type_tags: "[#{app.serve_content_type_tags.pluck(:name).join(',')}]"
      ) :
      self.class.keywords(content_type_tags: app.serve_content_type_tags.pluck(:name))
  end

  def self.keywords(content_type_tags: [])
    [
      { key: 'article_content', type: :string, desc: '文章内容一句话概述。' },
      { key: 'article_content_type', type: :array, desc: "文章内容的呈现形式数组。返回值为数组，数组元素为字符串。可选值有：[#{content_type_tags.join(',')}]，返回值必需在可选值中。在语句中没有明确指出时，返回空数组。" }
    ]
  end

  def answer(sentence)
    res = current_message.request_ai_response(prompt)
    generate_answer(res)
  end

  def ai_response_to_payload(res)
    article_content_type = res.dig('article_content_type') || []
    article_content = res.dig('article_content') || []

    serve_content_type_tag_ids = app.serve_content_type_tags.where(name: article_content_type).pluck(:id)

    serve_activity_ids = self.class.get_articles(
      article_content: article_content,
      serve_content_type_tag_ids: serve_content_type_tag_ids,
      app: app
    ).limit(5).map(&:id)

    {
      article_content: article_content,
      serve_content_type_tag_ids: serve_content_type_tag_ids,
      serve_activity_ids: serve_activity_ids
    }.with_indifferent_access
  end

  def generate_answer(res)
    result = ai_response_to_payload(res)
    article_content, serve_content_type_tag_ids, serve_activity_ids = result.values_at(:article_content, :serve_content_type_tag_ids, :serve_activity_ids)

    if serve_activity_ids.blank?
      {
        ai_response: res,
        result_type: 'search_article_empty',
        content: '没有找到符合条件的文章哦，请告诉我具体描述~'
        # mention_attributes: {},
      }
    else
      {
        ai_response: res,
        result_type: 'search_article_done',
        content: '找到符合条件的文章啦！'
        # mention_attributes: {
        #   article_content: article_content,
        #   serve_content_type_tag_ids: serve_content_type_tag_ids,
        #   serve_activity_ids: serve_activity_ids,
        # },
      }
    end
  end

  def self.get_articles(article_content:, serve_content_type_tag_ids: [], app: App.first)
    return app.serve_activities.none unless article_content.present?

    embedding = ::Chat::OpenaiService.embeddings([article_content]).first

    app.serve_activities.visible.published.joins(:content_type_tags).distinct.where(
      serve_content_type_tag_ids.present? ? { serve_content_type_tags: { id: serve_content_type_tag_ids } } : {}
    ).nearest_neighbors(
      :ai_summary_embedding, embedding, distance: 'euclidean'
    )
  end

  def need_mention?
    true
  end
end
