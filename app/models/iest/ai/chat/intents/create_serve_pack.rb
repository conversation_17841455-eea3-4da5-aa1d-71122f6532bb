class Iest::Ai::Chat::Intents::CreateServePack < ::Chat::Intent
  def prompt_summary
    super || '你是一个创建消息发送任务助手。请根据提供描述，提取消息发送任务所需信息。'
  end

  def keywords
    # to_keywords(
    #   res_tags: "[#{app.res_tags.pluck(:name).join(',')}]",
    #   content_type_tags: "[#{app.serve_content_type_tags.pluck(:name).join(',')}]",
    #   today: Date.today,
    # )
    self.class.keywords(
      content_type_tags: app.serve_content_type_tags.pluck(:name),
      res_tags: app.res_tags.pluck(:name)
    )
  end

  def self.keywords(content_type_tags: [], res_tags: [])
    [
      { key: 'send_at', type: :string, desc: "现在时间是#{Time.zone.now}，今年是#{Time.zone.now.year}年，根据描述得到具体时间，具体时间需大于#{Time.zone.now - 1.minute}，格式为“YYYY-MM-DD HH:MM:SS”，，在语句中没有明确指出时，返回 null。只指定了日期，没指定具体时分时，时分设置为09:00。当描述时间为“现在”时，返回#{Time.zone.now}" },
      { key: 'use_custom', type: :string, desc: '是否使用LLM自定义生成文案发送消息，而不是使用库里的文章，boolean 值，没有提及时，默认为 false。如果需要生成自定义内容，请按描述要求返回在 `custom_content` 字段中' },
      *Chat::Intents::ExpandStr.keywords,
      *Iest::Ai::Chat::Intents::SearchUser.keywords(res_tags: res_tags),
      *Iest::Ai::Chat::Intents::SearchServeActivity.keywords(content_type_tags: content_type_tags)
    ]
  end

  def need_mention?
    true
  end

  def answer(sentence)
    res = current_message.request_ai_response(prompt)
    generate_answer(res)
  end

  def present_or(a, b)
    a.present? ? a : b
  end

  def ai_response_to_payload(res)
    user_result = Iest::Ai::Chat::Intents::SearchUser.new(app: app).ai_response_to_payload(res)

    current_mention_payload = current_mention&.payload || {}

    use_custom = present_or(res.dig('use_custom'), current_mention_payload.dig('use_custom') || false)
    article_result = use_custom ? {} : Iest::Ai::Chat::Intents::SearchServeActivity.new(app: app).ai_response_to_payload(res)

    send_at = present_or(res.dig('send_at'), current_mention_payload.dig('send_at'))

    content = present_or(res.dig('custom_content'), current_mention_payload.dig('content') || nil)
    user_ids = present_or(user_result.dig('user_ids'), current_mention_payload.dig('payload', 'user_ids') || [])
    org_ids = present_or(user_result.dig('org_ids'), current_mention_payload.dig('payload', 'org_ids') || [])
    res_tag_ids = present_or(user_result.dig('res_tag_ids'), current_mention_payload.dig('payload', 'res_tag_ids') || [])
    article_content = present_or(article_result.dig('article_content'), current_mention_payload.dig('payload', 'article_content'))
    serve_content_type_tag_ids = present_or(article_result.dig('serve_content_type_tag_ids'), current_mention_payload.dig('payload', 'serve_content_type_tag_ids') || [])
    serve_activity_ids = present_or(article_result.dig('serve_activity_ids'), current_mention_payload.dig('payload', 'serve_activity_ids') || [])

    {
      send_at: send_at,
      content: content,
      payload: {
        use_custom: use_custom,
        user_ids: user_ids,
        org_ids: org_ids,
        res_tag_ids: res_tag_ids,
        serve_content_type_tag_ids: serve_content_type_tag_ids,
        serve_activity_ids: serve_activity_ids,
        article_content: article_content
      }
    }.with_indifferent_access
  end

  def generate_answer(res)
    result = ai_response_to_payload(res)

    if result.dig('send_at').blank?
      return {
        ai_response: res,
        result_type: 'create_serve_pack_send_at_empty',
        content: '没有确定发送时间哦，请告诉我具体描述~',
        mention_type: 'Iest::Ai::Chat::Mentions::ServePack',
        mentionable_type: 'Serve::Pack'
        # mention_attributes: result,
      }
    end

    if result.dig('payload', 'user_ids').blank?
      return {
        ai_response: res,
        result_type: 'create_serve_pack_user_ids_empty',
        content: '没有找到符合条件的用户哦，请告诉我具体描述~',
        mention_type: 'Iest::Ai::Chat::Mentions::ServePack',
        mentionable_type: 'Serve::Pack'
        # mention_attributes: result,
      }
    end

    if result.dig('payload', 'serve_activity_ids').blank? && result.dig('content').blank?
      return {
        ai_response: res,
        result_type: 'create_serve_pack_serve_activity_ids_empty',
        content: '没有找到符合条件的文章哦，请告诉我具体描述~',
        mention_type: 'Iest::Ai::Chat::Mentions::ServePack',
        mentionable_type: 'Serve::Pack'
        # mention_attributes: result,
      }
    end

    {
      ai_response: res,
      result_type: 'create_serve_pack_done',
      content: '已为您填写消息发送任务信息，请您确认发送。',
      mention_type: 'Iest::Ai::Chat::Mentions::ServePack',
      mentionable_type: 'Serve::Pack'
      # mention_attributes: result,
    }
  end
end
