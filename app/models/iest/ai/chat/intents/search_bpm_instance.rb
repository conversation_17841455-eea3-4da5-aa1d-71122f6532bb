class Iest::Ai::Chat::Intents::SearchBpmInstance < ::Chat::Intent
  def prompt_summary
    super || '你是一个搜索工作流程助手。请根据提供描述，提取搜索工作流程所需信息。'
  end

  def keywords
    intent_keywords.present? ? to_keywords : self.class.keywords
  end

  def self.keywords(res_tags: [])
    mode_desc = mode_map.map { |k, v| "#{k}：#{v}" }.join('，')
    [
      { key: 'mode', type: :string, desc: "工作流程的查看模式，可选值为#{mode_desc}" }
    ]
  end

  def answer(sentence)
    res = current_message.request_ai_response(prompt)
    generate_answer(res)
  end

  def self.mode_map
    {
      'created' => '您创建的',
      'notified' => '抄送给您的',
      'approving' => '待处理',
      'unread' => '未读',
      'approved' => '已处理'
    }
  end

  def need_mention?
    true
  end

  def ai_response_to_payload(res)
    mode = res.dig('mode')

    current_mode = ([mode] & self.class.mode_map.keys).first
    current_mode ||= 'approving'

    mode_zh = self.class.mode_map[current_mode]

    {
      mode: current_mode,
      mode_zh: mode_zh
    }.with_indifferent_access
  end


  def generate_answer(res)
    result = ai_response_to_payload(res)

    mode = result[:mode]
    mode_zh = result[:mode_zh]

    content = "已为您调出#{mode_zh}流程~"
    user = current_message.conversation.user

    if user
      count = 0

      case mode
      when 'created'
        count = app.bpm_instances.created_by(user.id).count
      when 'notified'
        count = app.bpm_instances.notified(user.id).count
      when 'approving'
        count = app.bpm_instances.approving(user.id).count
      when 'unread'
        count = app.bpm_instances.unread(user.id).count
      when 'approved'
        count = app.bpm_instances.approved(user.id).count
      end

      content = "您目前共有#{count}条#{mode_zh}流程"
    end

    {
      ai_response: res,
      result_type: 'search_instance_done',
      content: content,
      mention_type: 'Iest::Ai::Chat::Mentions::SearchBpmInstance',
      mention_attributes: {
        mode: mode
      }
    }
  end
end
