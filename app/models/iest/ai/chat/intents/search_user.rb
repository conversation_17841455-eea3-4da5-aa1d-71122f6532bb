class Iest::Ai::Chat::Intents::SearchUser < ::Chat::Intent
  def prompt_summary
    super || '你是一个搜索用户助手。请根据提供描述，提取搜索用户所需信息。'
  end

  def keywords
    intent_keywords.present? ?
      to_keywords(
        res_tags: "[#{app.res_tags.pluck(:name).join(',')}]"
      ) :
      self.class.keywords(res_tags: app.res_tags.pluck(:name))
  end

  def self.keywords(res_tags: [])
    [
      { key: 'user_name', type: :string, desc: '人员姓名。返回值为数组，数组元素为字符串。在语句中没有明确指出时，返回空数组。' },
      { key: 'user_department', type: :array, desc: '人员部门。返回值为数组，数组元素为字符串。需要是中国政府具体部门，在语句中没有明确指出时，返回空数组。对于“各部门”、“各单位”等描述，返回空数组。' },
      { key: 'user_tags', type: :array, desc: "人员标签。返回值为数组，数组元素为字符串。可选值有：[#{res_tags.join(',')}]，返回值必需在可选值中，在语句中没有明确指出时，返回空数组。" }
    ]
  end

  def need_mention?
    true
  end

  def answer(sentence)
    res = current_message.request_ai_response(prompt)
    generate_answer(res)
  end

  def ai_response_to_payload(res)
    user_name = res.dig('user_name')
    user_department = res.dig('user_department') || []
    user_tags = res.dig('user_tags') || []

    res_tag_ids = app.res_tags.where(name: user_tags).pluck(:id)
    org_ids = user_department.present? ?
                Tanent.current.orgs.ransack(
                  # app.orgs.ransack(
                  name_cont_any: user_department,
                  org_identity_org_type_eq: 'DingTalk'
                ).result.find_all_by_generation(1).pluck(:id) :
                []

    user_ids = Tanent.current.users.where(name: user_name).pluck(:id)
    # user_ids = app.users.where(name: user_name).pluck(:id)

    unless res_tag_ids.blank? && org_ids.blank?
      user_ids += Tanent.current.users.ransack(orgs_id_in: org_ids, res_tags_id_in: res_tag_ids).result.pluck(:id)
      # user_ids += app.users.ransack(orgs_id_in: org_ids, res_tags_id_in: res_tag_ids).result.pluck(:id)
    end

    {
      res_tag_ids: res_tag_ids,
      org_ids: org_ids,
      user_ids: user_ids
    }.with_indifferent_access
  end


  def generate_answer(res)
    result = ai_response_to_payload(res)

    user_ids, org_ids, res_tag_ids = result.values_at(:user_ids, :org_ids, :res_tag_ids)

    if user_ids.blank?
      {
        ai_response: res,
        result_type: 'search_user_empty',
        content: '没有找到符合条件的用户哦，请告诉我具体描述~'
        # mention_attributes: {},
      }
    else
      {
        ai_response: res,
        result_type: 'search_user_done',
        content: "找到符合条件的用户啦！一共#{user_ids.count}位用户。"
        # mention_attributes: {
        #   user_ids: user_ids,
        #   org_ids: org_ids,
        #   res_tag_ids: res_tag_ids,
        # },
      }
    end
  end
end
