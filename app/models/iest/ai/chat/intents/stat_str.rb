class Iest::Ai::Chat::Intents::StatStr < ::Chat::Intent
  def prompt_summary
    super || '你是一个统计表识别助手。请根据提供描述，提取统计所对应的表名。'
  end

  def keywords
    intent_keywords.present? ?
      to_keywords(
        res_tags: "[#{app.res_tags.pluck(:name).join(',')}]"
      ) :
      self.class.keywords
  end

  def self.keywords
    table_name_desc = table_name_map.map { |k, v| "#{k}：#{v}" }.join('，')
    [
      { key: 'table_name', type: :string, desc: "统计的表，可选值为#{table_name_desc}" }
    ]
  end

  def self.table_name_map
    {
      'user' => '人员库',
      'serve_activity' => '素材库',
      'serve_rule' => '规则库',
      'message' => '触达统计、推送统计'
    }
  end

  def need_mention?
    false
  end

  def answer(sentence)
    res = current_message.request_ai_response(prompt)
    generate_answer(res)
  end

  def ai_response_to_payload(res)
    table_name = res.dig('table_name')

    current_table_name = ([table_name] & self.class.table_name_map.keys).first
    table_name_zh = self.class.table_name_map[current_table_name]

    {
      table_name: current_table_name,
      table_name_zh: table_name_zh
    }.with_indifferent_access
  end


  def generate_answer(res)
    result = ai_response_to_payload(res)

    table_name, table_name_zh = result.values_at(:table_name, :table_name_zh)

    if table_name == 'user'
      content = <<-CONTENT
        总共有#{::User.count}用户，其中公职人员#{::User.ransack(res_tags_name_eq: '公职人员').result.distinct.count}人
      CONTENT
      {
        ai_response: res,
        result_type: 'stat_str_done',
        content: content
      }
    elsif table_name == 'serve_activity'
      content = <<-CONTENT
        廉洁素材库中目前共采集来自#{::Serve::Origin.count}个来源的统计，共有素材#{
          ::Serve::Activity.count
        }篇，其中图片类#{
          ::Serve::Activity.joins(:content_type_tags).where(serve_content_type_tags: { name: '图片' }).count
        }篇，视频类#{
          ::Serve::Activity.joins(:content_type_tags).where(serve_content_type_tags: { name: '视频' }).count
        }篇，文本类#{
          ::Serve::Activity.joins(:content_type_tags).where(serve_content_type_tags: { name: '文字' }).count
        }篇。推送素材共#{
          ::Serve::Pack.finished.count
          # ::Serve::Pack.finished.where.not(activity_id: nil).count
        }批次，触达#{
          ::Serve::Message.where(is_read: true).count
          # ::Serve::Message.joins(:pack).where.not(serve_packs: { activity_id: nil }).where(serve_packs: { state: 'finished' }).count
        }人次。
      CONTENT
      {
        ai_response: res,
        result_type: 'stat_str_done',
        content: content
      }
    elsif table_name == 'serve_rule'
      messages = ::Serve::Message.joins(:pack).where.not(serve_packs: { rule_id: nil })
      message_count = messages.count
      message_read_count = messages.where(is_read: true).count
      message_unread_count = message_count - message_read_count

      content = <<-CONTENT
        规则库中的规则共有#{
          ::Serve::RuleGroup.count
        }类，共有#{
          ::Serve::Rule.count
        }条规则，其中已审核发布批次共#{
          ::Serve::Pack.finished.joins(:rule).distinct.count
        }，待审核批次为#{
          ::Serve::Pack.joins(:rule).where(create_instance_state: 'processing').distinct.count
        }。
        共发送触达消息#{
          message_count
        }条，已读#{
          message_read_count
        }条，未读#{
          message_unread_count
        }条
      CONTENT
      {
        ai_response: res,
        result_type: 'stat_str_done',
        content: content
      }
    elsif table_name == 'message'
      messages = ::Serve::Message.all
      message_count = messages.count
      message_read_count = messages.where(is_read: true).count
      message_unread_count = message_count - message_read_count

      content = <<-CONTENT
        共发送触达消息#{
          message_count
        }条，已读#{
          message_read_count
        }条，未读#{
          message_unread_count
        }条。
        其中规则触发#{
          ::Serve::Message.joins(:pack).where.not(serve_packs: { rule_id: nil }).count
        }条，手动触发#{
          ::Serve::Message.joins(:pack).where(serve_packs: { rule_id: nil }).count
        }条。
      CONTENT
      {
        ai_response: res,
        result_type: 'stat_str_done',
        content: content
      }
    else
      {
        ai_response: res,
        result_type: 'stat_str_empty',
        content: '没有找到符合条件的统计表哦，请告诉我具体描述~'
        # mention_attributes: {},
      }
    end
  end
end
