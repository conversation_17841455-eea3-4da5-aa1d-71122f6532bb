class Iest::Ai::Chat::IntentKeyword < ApplicationRecord
  self.track_migration = true

  belongs_to :intent, class_name: 'Iest::Ai::Chat::Intent'

  attribute :name, :string, comment: '关键词名称'
  attribute :desc, :string, comment: '关键词描述'
  attribute :content_type, :string, comment: '关键词类型' # array, string

  scope :to_keywords, ->(**opts) {
    all.map do |ik|
      ik.to_keyword(**opts)
    end
  }

  def to_keyword(**opts)
    {
      key: name,
      desc: formatted_desc(**opts),
      type: content_type
    }
  end

  def formatted_desc(**opts)
    desc.gsub(/{{(.*?)}}/) do |m|
      opts[$1.to_sym]
    end
  end
end
