class Iest::Ai::OpenaiService
  API_KEY = ENV['OPENAI_API_KEY']
  BASE_URL = ENV['OPENAI_BASE_URL']

  def self.with_rescue(exceptions, retries: 5)
    try = 0
    begin
      yield try
    rescue *exceptions => exc
      try += 1
      try <= retries ? retry : raise
    end
  end

  class DownloadImageTimeoutError < Error::BaseError; end

  def self.completions(data)
    # 设置API密钥

    # 设置请求头
    headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{API_KEY}"
    }

    # # 设置请求数据
    # data = {
    #   model: "gpt-3.5-turbo",
    #   messages: [{ role: "user", content: "Say this is a test!" }],
    #   temperature: 0.7
    # }.to_json

    # 发起请求
    with_rescue([DownloadImageTimeoutError], retries: 3) do |try|
      Rails.logger.info("`LLM` request: #{data}")
      response = Typhoeus.post(
        File.join(BASE_URL, '/v1/chat/completions'),
        headers: headers,
        body: data.to_json
      )

      # 打印响应
      json = JSON.parse(response.body)
      Rails.logger.info("LLM response: #{json}")
      result = json.dig('choices', 0, 'message', 'content')
      return result if result
      if json.dig('error', 'message').include?('Timeout while downloading')
        raise DownloadImageTimeoutError.new(message: '下载图片超时')
      end
      p "response error: #{json}"
      raise Error::BaseError.new(message: '请求失败')
    end
  end

  def self.simple_request(prompt:, sentence:, model: 'gpt-4-turbo')
    body = self.completions({
      model: 'gpt-4-turbo',
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: sentence }
      ],
      response_format: { type: 'json_object' }
    })

    JSON.parse(body)
  end

  # input array
  def self.embeddings(input)
    headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{API_KEY}"
    }

    data = {
      input: input,
      model: 'text-embedding-3-small'
    }

    response = Typhoeus.post(
      File.join(BASE_URL, '/v1/embeddings'),
      headers: headers,
      body: data.to_json
    )

    JSON.parse(response.body)['data'].map { |v| v['embedding'] }
  end
end
