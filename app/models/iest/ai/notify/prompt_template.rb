class Iest::Ai::Notify::PromptTemplate
  # 意图识别
  def self.intent
    prompt = <<-PROMPT
      **你是一个意图识别助手。请根据提供对话，判断用户的下一步意图。**
      json 返回，格式为 { "intent": "" }
      #### `intent` 可选值为：
        1. `create_notify`: 创建消息发送任务(包含发送时间、文章、用户)
        2. `search_article`: 搜索文章
        3. `search_user`: 搜索用户
        4. `change_article`: 修改文章
        5. `other`: 其他
    PROMPT
  end

  def self.keywords_to_prompt(keywords)
    keywords.map do |keyword|
      "#### `#{keyword[:key]}` #{keyword[:desc]}"
    end.join("\n")
  end

  def self.keywords_to_format(keywords)
    "{#{
      keywords.map do |keyword|
        "\"#{keyword[:key]}\": #{keyword[:type] == :array ? '[]' : '""'}"
      end.join("\n")}
    }"
  end

  attr_reader :prompt

  def initialize(prompt_summary, keywords)
    @prompt = <<-PROMPT
      **#{prompt_summary}**
      json 返回，格式为 #{self.class.keywords_to_format(keywords)}

      #{
        self.class.keywords_to_prompt(keywords)
      }
    PROMPT
  end
end
