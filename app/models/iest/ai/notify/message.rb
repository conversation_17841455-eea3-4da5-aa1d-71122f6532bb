class Iest::Ai::Notify::Message
  # meta: 已经获取的数据
  def self.create(sentence, history: [], meta: {}, app: App.first)
    # 意图识别
    intent = request_intent(sentence: sentence, history: history).dig('intent')
    # 执行意图
    res = case intent
    when 'create_notify'
        Iest::Ai::Notify::MessageResponse::CreateNotify.new(sentence: sentence, meta: meta, app: app)
    when 'search_article'
        Iest::Ai::Notify::MessageResponse::SearchArticle.new(sentence: sentence, meta: meta, app: app)
    when 'search_user'
        Iest::Ai::Notify::MessageResponse::SearchUser.new(sentence: sentence, meta: meta, app: app)
    when 'change_article'
        Iest::Ai::Notify::MessageResponse::ChangeArticle.new(sentence: sentence, meta: meta, app: app)
    else
        Iest::Ai::Notify::MessageResponse::Other.new(sentence: sentence, meta: meta, app: app)
    end

    res.answer
  end

  def self.request_intent(sentence:, history: [])
    body = ::Chat::OpenaiService.completions({
      model: 'gpt-4-turbo',
      messages: [
        { role: 'system', content: Iest::Ai::Notify::PromptTemplate.intent },
        *history.map { |h| { role: 'user', content: h } },
        { role: 'user', content: sentence }
      ],
      response_format: { type: 'json_object' }
    })

    JSON.parse(body)
  end
  # def self.process_create_notify(res, app: App.first)
  #   send_at = res.dig("send_at")
  # end

  # def self.process_search_article(res, app: App.first)
  #   article_content = res.dig("article_content")
  #   article_content_type = res.dig("article_content_type") || []
  # end
end
