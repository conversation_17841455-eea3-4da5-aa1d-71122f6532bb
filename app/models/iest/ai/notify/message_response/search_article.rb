class Iest::Ai::Notify::MessageResponse::SearchArticle
  PROMPT_SUMMARY = '你是一个搜索文章助手。请根据提供描述，搜索文章。'

  def self.keywords(content_type_tags: [])
    [
      { key: 'article_content', type: :string, desc: '文章内容一句话概述。' },
      { key: 'article_content_type', type: :array, desc: "文章内容的呈现形式数组。返回值为数组，数组元素为字符串。可选值有：[#{content_type_tags.join(',')}]，返回值必需在可选值中。在语句中没有明确指出时，返回空数组。" }
    ]
  end

  def initialize(sentence: '', meta: {}, app: App.first)
    @sentence = sentence
    @meta = meta
    @app = app
  end

  def answer
    @prompt = Iest::Ai::Notify::PromptTemplate.new(PROMPT_SUMMARY, self.class.keywords(content_type_tags: @app.serve_content_type_tags.pluck(:name))).prompt
    res = current_message.request_ai_response(@prompt, sentence: @sentence)
    result = process_ai_response(res)
    answer_search_article(result)
  end

  def process_ai_response(res)
    article_content_type = res.dig('article_content_type') || []
    article_content = res.dig('article_content') || []

    serve_content_type_tag_ids = @app.serve_content_type_tags.where(name: article_content_type).pluck(:id)

    serve_activities = self.class.get_articles(
      article_content: article_content,
      serve_content_type_tag_ids: serve_content_type_tag_ids,
      app: @app
    ).limit(5).map do |article|
      {
        id: article.id,
        name: article.name
      }
    end

    {
      article_content: article_content,
      serve_content_type_tag_ids: serve_content_type_tag_ids,
      serve_activities: serve_activities
    }
  end

  def answer_search_article(result)
    article_content, serve_content_type_tag_ids, serve_activities = result.values_at(:article_content, :serve_content_type_tag_ids, :serve_activities)

    if serve_activities.blank?
      {
        action: 'search_article_empty',
        message: '没有找到符合条件的文章哦，请告诉我具体描述~',
        meta: {}
      }
    else
      {
        action: 'search_article_done',
        message: '找到符合条件的文章啦！',
        meta: {
          article_content: article_content,
          serve_content_type_tag_ids: serve_content_type_tag_ids,
          serve_activities: serve_activities
        }
      }
    end
  end

  def self.get_articles(article_content:, serve_content_type_tag_ids: [], app: App.first)
    return app.serve_activities.none unless article_content.present?

    embedding = ::Chat::OpenaiService.embeddings([article_content]).first

    app.serve_activities.visible.published.joins(:content_type_tags).distinct.where(
      serve_content_type_tag_ids.present? ? { serve_content_type_tags: { id: serve_content_type_tag_ids } } : {}
    ).nearest_neighbors(
      :ai_summary_embedding, embedding, distance: 'euclidean'
    )
  end
end
