class Iest::Ai::Notify::MessageResponse::CreateNotify
  PROMPT_SUMMARY = '你是一个创建消息发送任务助手。请根据提供描述，提取消息发送任务所需信息。'

  def self.keywords(content_type_tags: [], res_tags: [])
    [
      { key: 'send_at', type: :string, desc: "今天为#{Date.today}，根据描述得到具体时间，格式为“YYYY-MM-DD HH:MM:SS”，，在语句中没有明确指出时，返回 null" },
      *Iest::Ai::Notify::MessageResponse::SearchUser.keywords(res_tags: res_tags),
      *Iest::Ai::Notify::MessageResponse::SearchArticle.keywords(content_type_tags: content_type_tags)
    ]
  end

  def initialize(sentence: '', meta: {}, app: App.first)
    @sentence = sentence
    @meta = meta
    @app = app
  end

  def answer
    @prompt = Iest::Ai::Notify::PromptTemplate.new(
      PROMPT_SUMMARY,
      self.class.keywords(content_type_tags: @app.serve_content_type_tags.pluck(:name), res_tags: @app.res_tags.pluck(:name))
    ).prompt
    res = current_message.request_ai_response(@prompt, sentence: @sentence)

    send_result = { send_at: res.dig('send_at') }
    user_result = Iest::Ai::Notify::MessageResponse::SearchUser.new.process_ai_response(res)
    article_result = Iest::Ai::Notify::MessageResponse::SearchArticle.new.process_ai_response(res)

    answer_create_notify(send_result: send_result, user_result: user_result, article_result: article_result)
  end

  def get_value_or_from_meta(key, res, type: :string)
    res.dig(key) || @meta[key] || (type == :array ? [] : nil)
  end

  def answer_create_notify(send_result:, user_result:, article_result:)
    send_at = get_value_or_from_meta(:send_at, send_result)
    user_ids = get_value_or_from_meta(:user_ids, user_result, type: :array)
    org_ids = get_value_or_from_meta(:org_ids, user_result, type: :array)
    res_tag_ids = get_value_or_from_meta(:res_tag_ids, user_result, type: :array)
    article_content = get_value_or_from_meta(:article_content, article_result)
    serve_content_type_tag_ids = get_value_or_from_meta(:serve_content_type_tag_ids, article_result, type: :array)
    serve_activities = get_value_or_from_meta(:serve_activities, article_result, type: :array)

    meta_data = {
      send_at: send_at,
      user_ids: user_ids,
      org_ids: org_ids,
      res_tag_ids: res_tag_ids,
      serve_content_type_tag_ids: serve_content_type_tag_ids,
      serve_activities: serve_activities
    }.reject { |_, v| v.blank? }

    if send_at.blank?
      return {
        action: 'create_notify_empty',
        message: '没有确定发送时间哦，请告诉我具体描述~',
        meta: meta_data
      }
    end

    if user_ids.blank?
      return {
        action: 'create_notify_empty',
        message: '没有找到符合条件的用户哦，请告诉我具体描述~',
        meta: meta_data
      }
    end

    if serve_activities.blank?
      return {
        action: 'create_notify_empty',
        message: '没有找到符合条件的文章哦，请告诉我具体描述~',
        meta: meta_data
      }
    end

    {
      action: 'create_notify_done',
      message: '已为您填写消息发送任务信息，请您确认发送。',
      meta: meta_data
    }
  end
end
