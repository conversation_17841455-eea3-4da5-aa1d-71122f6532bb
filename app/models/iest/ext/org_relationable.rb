
module Iest::Ext::OrgRelationable
  extend ActiveSupport::Concern

  included do
    has_many :org_relations, as: :source, dependent: :destroy
    has_many :direct_orgs, through: :org_relations, source: :org

    # 查找所有配置的orgs(包含subtree模式下的子组织)
    def all_related_orgs
      # 获取self模式的直接关联组织
      self_mode_orgs = org_relations.itself.includes(:org).map(&:org)
      # 获取subtree模式下的所有子组织
      subtree_orgs = org_relations.subtree.includes(:org).flat_map do |relation|
        relation.org&.self_and_descendants || []
      end

      # 合并并去重
      (self_mode_orgs + subtree_orgs).uniq
    end
  end
end
