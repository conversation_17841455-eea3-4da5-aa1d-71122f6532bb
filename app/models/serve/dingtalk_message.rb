class Serve::DingtalkMessage < Serve::Message
  after_create :generate_dingtalk_message!

  def generate_dingtalk_message!
    client_id  = rule&.options&.client_id
    oauth_app_id = client_id.present? ? Dingtalk::Client.find(client_id).code : user.orgs.find do |org|
      org.model_payload.dig("oauth_app_id")
    end&.model_payload&.dig("oauth_app_id") || ENV['DINGTALK_NOTIFY_APP_CODE']
    Dingtalk::TemplateMessage.create!(
      seq: seq,
      notifyable: self,
      oauth_app_id: oauth_app_id,
      user: user,
      message: body,
    )
  end
end
