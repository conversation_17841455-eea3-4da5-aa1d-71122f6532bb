module Serve::Model::Message
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    broadcasts

    seqable
    stiable
    # associaton
    belongs_to :app

    belongs_to :notifyable, polymorphic: true, optional: true
    belongs_to :user, class_name: '::User' # 接受者
    belongs_to :sender, class_name: '::User', optional: true # 发送者
    belongs_to :org, class_name: '::Org', optional: true
    belongs_to :pack,     optional: true
    belongs_to :activity, optional: true
    belongs_to :rule, optional: true
    belongs_to :ai_message, optional: true

    has_many :dingtalk_messages, as: :notifyable, class_name: 'Dingtalk::TemplateMessage'

    has_event :read

    before_destroy :revoke_dingtalk_messages

    attribute :name,     :string,   comment: '名称'
    attribute :state,    :string,   comment: '状态'
    attribute :content,  :text,     comment: '内容'
    attribute :send_at,  :datetime, comment: '发送时间'
    attribute :body,     :jsonb,    comment: '发送信息内容'
    attribute :response, :jsonb,    comment: '发送结果'
    attribute :payload,  :jsonb,    comment: '其他内容'

    enum state: { successed: 'successed', failed: 'failed' }

    default_value_for(:app) { |o| o.user&.app }
    default_value_for(:org) { |o| o.pack&.org }
    default_value_for(:state) { 'failed' }
    default_value_for(:rule) { |o| o.pack&.rule }

    after_commit :relate_to_ai_message_square!, on: :create

    scope :current_tanent, -> { Tanent.current ? ransack(pack_tanents_id_eq: Tanent.current&.id).result.distinct : all }

    scope :sreen_by_period, ->(period = 10, unit = 'week') {
      result, date = [], Date.today
      period.times do |i|
        start_time = i.try(unit).ago(date.to_datetime).try("beginning_of_#{unit}")
        end_time = i.try(unit).ago(date.to_datetime).try("end_of_#{unit}")
        result.push({
          cweek: start_time.to_date.cweek,
          start_day: start_time.to_date.strftime('%F'),
          end_of_day: end_time.to_date.strftime('%F'),
          pack_count: between_times(start_time, end_time).count
        })
      end
      result
    }

    def org_name
      pack&.org&.name
    end

    def rule_name
      pack&.rule&.name
    end

    def relate_to_ai_message_square!
      org_names = %w[杭州市 上城区 西湖区 萧山区 余杭区 临平区 富阳区 临安区 桐庐县 淳安县 建德市 滨江区 钱塘区 拱墅区]
      return nil unless org&.name.to_s.in?(org_names)

      if ai_message.blank?
        # 如果使用的是消息库
        if rule.options.message_template_enabled && rule.message_templates.count > 0
          rule.message_templates.each do |message_template|
            message_template.option.content.templates.each do |template|
              next if template.content.blank?
              ai_message_square = Serve::AiMessageSquare.find_or_initialize_by(rule: rule, content: template.content)
              ai_message_square.update(name: rule.name) unless ai_message_square.persisted?

              template = self.payload&.dig('content', 'template')
              next if template.blank?
              self.update(ai_message: ai_message_square) if ai_message_square.content == template
            end
          end
        else
          rule_item = pack&.rule_item
          return nil unless rule_item

          content = rule_item.template_message
          return nil if content.blank?

          ai_message_square = Serve::AiMessageSquare.find_or_initialize_by(rule: rule, app_id: 1, creator_id: 1, content: content)
          ai_message_square.update(name: rule.name) unless ai_message_square.persisted?
          self.update(ai_message: ai_message_square)
        end
      end
    end

    include Bot::Searchable
    # 定义可搜索的属性
    searchable_attributes :state

     # 定义可搜索的关联
     searchable_associations :org, :rule, :user, :pack, :activity, :ai_message

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        state: {
          type: :attribute,
          name: human_attribute_name(:state),
          column_type: :string,
          description: '消息状态',
          values: ['发送成功(successed)', '发送失败(failed)']
        },
        created_at: {
          type: :attribute,
          name: human_attribute_name(:created_at),
          column_type: :datetime,
          description: '消息时间'
        },
        org: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '组织名称'
          }
        },
        rule: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '规则名称'
          }
        },
        user: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '消息接收人姓名',
            account: '消息接收人账号'
          }
        },
        pack: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: 'pack名称'
          }
        },
        activity: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '素材名称'
          }
        },
        ai_message: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '消息广场名称'
          }
        }
      }
    end

    private

    # 在销毁Message前撤回关联的钉钉消息
    def revoke_dingtalk_messages
      if respond_to?(:dingtalk_messages) && dingtalk_messages.any?
        dingtalk_messages.each do |dingtalk_message|
          begin
            Rails.logger.info("撤回钉钉消息: #{dingtalk_message.inspect}")
            dingtalk_message.revoke
          rescue => e
            Rails.logger.error("撤回钉钉消息失败: #{e.message}")
          end
        end
      end
    end
  end
end
