module Serve::Model::AiMessage
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable
    seqable

    include Favor::Ext::Markable
    acts_as_markable(flag: 'like', require_folder: false)
    acts_as_markable(require_folder: false)

    has_event :hot

    acts_as_list scope: [:app_id, :type]

    belongs_to :app
    belongs_to :creator, class_name: '::User'
    belongs_to :org,     class_name: '::Org', optional: true
    belongs_to :pack,    optional: true
    belongs_to :rule,    optional: true
    belongs_to :ref_ai_message, class_name: 'Serve::AiMessage', optional: true

    has_many :messages, class_name: 'Serve::Message', dependent: :nullify

    attribute :name,        :string,   comment: '名称'
    attribute :state,       :string,   comment: '状态'
    attribute :content,     :text,     comment: '内容'
    attribute :option,      :jsonb,    comment: '配置'
    attribute :payload,     :jsonb,    comment: '其他字段'
    attribute :position,    :integer,  comment: '排序'
    attribute :stars_count, :integer,  comment: '收藏人数'
    attribute :likes_count, :integer,  comment: '点赞人数'

    default_value_for(:app){ |o| o.creator&.app || o.pack&.app }
    default_value_for(:org){ |o| o.pack&.org || o.creator&.orgs&.first }
    default_value_for(:is_hotted){ false }

    enum state: { draft: 'draft', published: 'published', closed: 'closed' }
    default_value_for(:state) { 'published' }

    def format_content user: nil, source: nil
      data = {
        user_name: user&.name,
        source_name: source.try(:name),
      }
      format(content, data)
    end

    def used_count
      messages.count
    end
  end
end

