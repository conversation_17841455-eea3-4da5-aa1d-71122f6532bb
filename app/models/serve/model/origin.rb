module Serve::Model::Origin
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    formable
    stiable
    acts_as_list scope: [:app_id]
    # associaton
    belongs_to :app
    belongs_to :org, optional: true, class_name: '::Org'
    belongs_to :submodule, optional: true

    has_many :activities, dependent: :nullify
    # attribute
    attribute :name,       :string,  comment: '名称'
    attribute :state,      :string,  comment: '状态'
    attribute :code,       :string,  comment: '标识'
    attribute :mode,       :string,  comment: '分类'
    attribute :position,   :integer, comment: '排序'
    attribute :payload,    :jsonb,   comment: '其他字段'
    attribute :latest_send_at, :datetime, comment:  '最新来源时间'

    enum state: { draft: 'draft', used: 'used', closed: 'closed' }
    default_value_for(:state) { 'used' }
    default_value_for(:app) { |o| o.org&.app }

    scope :sreen_by_day, ->(code = '网站', period = 7) {
      result = {}
      origin_ids = Serve::Activity.between_times(period.days.ago.beginning_of_day, Time.now.end_of_day).pluck(:origin_id)
      where(id: origin_ids, code: code).find_each { |origin| result[origin.name] = origin.activities_count }
      result
    }

    def activities_count
      activities.count
    end
  end
end
