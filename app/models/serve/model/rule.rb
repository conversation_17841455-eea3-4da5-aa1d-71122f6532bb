module Serve::Model::Rule
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable
    formable
    effectable

    include Schedule::Ext::Ruleable
    has_many :rule_records, class_name: 'Serve::RuleRecord', as: :rule, dependent: :destroy

    class Options
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      class Scope
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :key,    :string
        attr_json :val,    :string
        attr_json :scope_type,    :string
        attr_json :method,        :string
      end

      class Message
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        # 浙政钉消息模版-用户收到的消息
        attr_json :enabled, :boolean, default: false
        attr_json :template_message_title,   :string
        attr_json :template_message_body,    :string
        attr_json :template_message_body_scopes,  Scope.to_type, array: true, default: []
        # 浙政钉消息点击详情 例如 发送(【姓名 + 职务】您好,您的招标项目【项目名称】)
        attr_json :template_message,   :string
        attr_json :templates, Scope.to_type, array: true, default: []
      end

      ### 版本即将废除 ###
      class Setting
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :send_at, :string
        attr_json :user_scopes,     Scope.to_type, array: true, default: []
        attr_json :activity_scopes, Scope.to_type, array: true, default: []
      end
      ### 版本即将废除 ###

      class Activity
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :scopes, Scope.to_type, array: true, default: []
      end

      class Bpm
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :enabled, :boolean, default: false
        attr_json :generate_instance, :boolean, default: true
        attr_json :workflow_id, :integer
      end

      class TemplatePrompt
        include AttrJson::Model
        attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

        attr_json :prompt,   :string
      end

      attr_json :generate_instance,   :boolean, default: true
      attr_json :message_enabled,      :boolean, default: false
      attr_json :template_message_title,   :string
      attr_json :template_message_body,    :string
      attr_json :template_message_body_scopes,  Scope.to_type, array: true, default: []
      # 配置模版消息 例如 发送(【姓名 + 职务】您好,您的招标项目【项目名称】)
      attr_json :template_message,   :string
      attr_json :templates, Scope.to_type, array: true, default: []

      attr_json :message, Message.to_type, default: {}
      attr_json :relate_klass, :string
      attr_json :relate_scopes,   Scope.to_type, array: true, default: []
      attr_json :settings, Setting.to_type, array: true, default: []
      attr_json :client_id, :integer
      attr_json :website_url_tanent_id, :integer
      attr_json :template_content_type, :string, default: 'prompt' # prompt, content, activity
      attr_json :prompt, :string
      attr_json :template_prompt, TemplatePrompt.to_type, default: {}
      attr_json :activity, Activity.to_type, default: {}
      attr_json :bpm, Bpm.to_type, default: {}
      attr_json :message_template_enabled, :boolean, default: false
    end

    # association
    belongs_to :app
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :catalog, optional: true
    belongs_to :rule_group, optional: true
    # belongs_to :tanent, class_name: '::Tanent', optional: true

    has_many :packs, -> { current_tanent }, dependent: :destroy
    has_many :messages, -> { current_tanent }, dependent: :destroy
    has_many :rule_items, dependent: :destroy
    accepts_nested_attributes_for :rule_items, allow_destroy: true, reject_if: :all_blank

    has_many :message_templates, dependent: :destroy
    accepts_nested_attributes_for :message_templates, allow_destroy: true, reject_if: :all_blank

    has_many :serve_ai_message_squares,  class_name: 'Serve::AiMessageSquare'
    has_many :serve_ai_message_templates,  class_name: 'Serve::AiMessageTemplate'
    has_many :serve_ai_messages, class_name: 'Serve::AiMessage'


    acts_as_list scope: [:app_id]

    # attribute
    attribute :name,     :string, comment: '名称'
    attribute :state,    :string, comment: '状态'
    attribute :batch_no, :integer, comment: '批次'
    attribute :code,     :string, comment:  '编号'
    attribute :position, :integer, comment: '排序'
    attribute :content,  :jsonb,   comment:  '内容'
    attribute :latest_send_at, :datetime, comment:  '最新发送时间'
    attribute :message_type, :string, comment:  '消息类型'
    attribute :options, :jsonb, comment: '配置'
    serialize :options, coder: Options.to_serialization_coder, default: {}
    attribute :description, :text, comment: '工具描述'

    enum state: { draft: 'draft', used: 'used', closed: 'closed' }
    default_value_for(:state) { 'draft' }
    default_value_for(:rule_record_type) { 'Serve::RuleRecord' }

    accepts_nested_attributes_for :rule_items, allow_destroy: true, reject_if: :all_blank

    include Iest::Ext::OrgRelationable
    accepts_nested_attributes_for :org_relations, allow_destroy: true, reject_if: :all_blank

    scope :current_tanent, -> { Tanent.current ? relate_tanent_action_targets_any(Tanent.current) : all }

    scope :screen_for_org, ->(rule_group_id = nil) {
      result = {}
      ransack(rule_group_id_eq: rule_group_id).result.each do |rule|
        arr = []
        rule.orgs.each do |org|
          packs = rule.packs.where(org: org)
          arr.push({
            name: org.name,
            packs_count: packs.count,
            finished_packs_count: packs.finished.count
          })
        end
        result[rule.name] = arr
      end
      result
    }

    # 默认的发送区域
    action_store(
      :relate,
      :org,
      class_name: '::Org',
      action_class_name: 'Serve::OrgAction',
      alias_name: 'orgs',
      inverse_alias_name: 'rules',
    )

    # 关联的租户
    action_store(
      :relate_tanent,
      :tanent,
      class_name: '::Tanent',
      action_class_name: 'Serve::TanentAction',
      alias_name: 'tanents',
      inverse_alias_name: 'serve_rules',
    )

    # 关联的素材文章
    action_store(
      :relate,
      :activity,
      class_name: 'Serve::Activity',
      action_class_name: 'Serve::RuleAction',
      alias_name: 'activities',
      inverse_alias_name: 'rules',
    )

    def generate_pack!(rule_record: nil)
      rule_items.used.each { |item| item.generate_pack!(rule_record: rule_record) }
    end

    def total_packs_count
      packs.count
    end

    def total_rule_records_count
      rule_records.count
    end

    def statistic
      packs.group(:create_instance_state).count
    end

    def processing_packs_count
      packs.where(state: ['pending']).count
    end

    def finished_packs_count
      packs.finished.count
    end

    def relate?
      code == 'relate'
    end

    def query_activities
      association = app.serve_activities
      options.activity.scopes.each do |scope|
        if scope.scope_type == 'ransack'
          val = scope.key&.end_with?('_in') || scope.key&.end_with?('cont_any') ? scope.val.split(',') : scope.val
          association = association.ransack({ scope.key => val }).result
        else
          association = association.try(scope.method, *scope.val.to_s.split(','))
        end
      end
      association
    end

    # 是否需要生成实例
    def require_generate_instance?
      options.bpm&.enabled ? options.bpm.generate_instance : false
    end

    def generate_ai_pack!(current_user:, user_ids: [], content: nil, send_at: nil, message: {})
      activity, payload = nil, nil
      # 优先使用指定素材
      if content.blank?
        if options.template_content_type == 'prompt'
          # 使用ai生成内容
          contents = generate_content_by_template_prompt
          if contents.present?
            content = contents.first
            payload = {
              user_ids: user_ids,
              use_ai: true, # 使用ai生成内容
              contents: contents,
              message: message
            }
          end
        elsif options.template_content_type == 'activity'
          activity_ids = self.activity_ids.presence
          activity_ids = query_activities.pluck(:id) unless activity_ids
          payload = {
            user_ids: user_ids,
            activity_ids: activity_ids,
            use_activity: true, # 使用素材
            message: message
          }
          activity = app.serve_activities.find_by(id: activity_ids.sample)
        end
      else
        payload = {
          user_ids: user_ids,
          use_custom: true, # 使用自定义内容
          message: message
        }
      end

      self.packs.create!(
        name: ["【#{send_at}】", self.name] * '',
        send_at: send_at,
        activity: activity,
        content: content,
        org: current_user&.orgs&.first,
        creator: current_user,
        message_type: message_type,
        state: options&.generate_instance ? 'pending' : 'sending',
        tanent_ids: self.tanent_ids,
        payload: payload
      )
    end

    # 根据templete prompt配置，生成5条候选内容
    # 返回结果是个数组
    def generate_content_by_template_prompt(prompt: nil, mode: nil)
      start_time = Time.current
      deepseek_mode = get_deepseek_mode_key(mode)

      Rails.logger.info "开始AI内容生成，使用DeepSeek模式: #{deepseek_mode}"

      begin
        llm = Bot::LlmFactory.create(deepseek_mode)
        response = llm.chat(
          messages: [
            {
              role: 'system',
              content: '请根据用户的需求生成5条候选内容，设置的字数是每条内容的字数，不是总字数，如果没有设置字数，那就每条内容200字左右。请以JSON格式返回，格式为：{"contents": ["内容1", "内容2", "内容3", "内容4", "内容5"]}'
            },
            {
              role: 'user',
              content: prompt || options&.prompt || options&.template_prompt&.prompt || name # 如果配置了template_prompt，则使用template_prompt，否则使用规则名称
            }
          ],
          response_format: { type: 'json_object' }
        )

        duration = Time.current - start_time
        Rails.logger.info "AI内容生成完成，DeepSeek模式: #{deepseek_mode}，耗时: #{duration.round(2)}秒"

        # 记录性能指标
        expected_timeout = get_deepseek_mode_timeout(deepseek_mode)
        if duration > expected_timeout
          Rails.logger.warn "DeepSeek #{deepseek_mode} 模式响应时间过长: #{duration.round(2)}秒，预期: #{expected_timeout}秒"
        end

        JSON.parse(response.chat_completion)['contents']
      rescue => e
        duration = Time.current - start_time
        Rails.logger.error "AI内容生成失败，DeepSeek模式: #{deepseek_mode}，耗时: #{duration.round(2)}秒，错误: #{e.message}"
        raise e
      end
    end

    # 获取DeepSeek模式键值（简化版本，直接接收参数）
    def get_deepseek_mode_key(mode = nil)
      # 如果传入了mode参数，优先使用
      if mode.present?
        mode_key = mode.to_s.to_sym
        return mode_key if available_deepseek_modes.key?(mode_key)
      end

      # 否则检查配置
      configured_mode = options&.dig('deepseek_mode')
      if configured_mode && available_deepseek_modes.key?(configured_mode.to_sym)
        return configured_mode.to_sym
      end

      # 默认使用标准模式
      :deepseek
    end

    # 可用的DeepSeek模式配置
    def available_deepseek_modes
      {
        deepseek: {
          name: 'DeepSeek 标准模式',
          description: '标准推理模式，速度相对较快',
          speed: 'medium',
          quality: 'good',
          timeout: 120,
          icon: '⚡'
        },
        deepseek_r1: {
          name: 'DeepSeek R1 深度推理',
          description: '深度推理模式，质量更高但速度较慢',
          speed: 'slow',
          quality: 'excellent',
          timeout: 300,
          icon: '🧠'
        }
      }
    end

    # 获取DeepSeek模式的预期超时时间
    def get_deepseek_mode_timeout(mode)
      available_deepseek_modes.dig(mode, :timeout) || 120
    end

    # 获取当前DeepSeek模式信息（用于前端显示）
    def get_deepseek_mode_info
      mode = get_deepseek_mode_key
      available_deepseek_modes[mode] || available_deepseek_modes[:deepseek]
    end

    # 根据模式键值获取模式信息
    def get_deepseek_mode_info_by_key(mode_key)
      available_deepseek_modes[mode_key.to_sym] || available_deepseek_modes[:deepseek]
    end

    # 获取卡片内容
    def get_card_content(user: nil, source: nil)
      return nil if options.template_message_body.blank?
      scopes = options&.template_message_body_scopes || []

      data = scopes.each_with_object({}) do |t, h|
              h[t.val.to_sym] = t.key == 'user' ? user.try_method(t.method) : source.try_method(t.method)
            end
      format(options.template_message_body, data)
    end

    def get_content(user: nil, source: nil)
      return nil if options.template_message.blank?

      scopes = options&.templates || []
      data = scopes.each_with_object({}) do |t, h|
              h[t.val.to_sym] = t.key == 'user' ? user.try_method(t.method) : source.try_method(t.method)
            end
      format(options.template_message, data)
    end

    # 最后一个pack
    def last_pack
      packs.order(created_at: :desc).first
    end

    # 最后一个message
    def last_message
      messages.order(created_at: :desc).first
    end

    include Bot::Searchable
    # 定义可搜索的属性
    searchable_attributes :name, :state

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        name: {
          type: :attribute,
          name: human_attribute_name(:name),
          column_type: :string,
          description: '名称'
        },
        state: {
          type: :attribute,
          name: human_attribute_name(:state),
          column_type: :string,
          description: '规则状态',
          values: ['草稿(draft)', '使用中(used)', '已关闭(closed)']
        }
      }
    end
  end

  class_methods do
    # 给ai调用
    def generate_ai_pack!(uuid: nil, user_ids: [], activity_ids: [], org: nil, send_at: nil)
      item = Serve::RuleItem.find_by uuid: uuid
      item.generate_ai_pack! user_ids: user_ids, activity_ids: activity_ids, org: org, send_at: send_at
    end
  end
end
