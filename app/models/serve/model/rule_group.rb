module Serve::Model::RuleGroup
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    # associaton
    belongs_to :app
    belongs_to :submodule, optional: true

    acts_as_list scope: [:app_id]

    has_many :rules, -> { current_tanent }, dependent: :nullify
    has_many :rule_records, -> { current_tanent }, through: :rules
    has_many :packs, -> { current_tanent }, through: :rules
    has_many :messages, -> { current_tanent }, through: :rules
    has_many :serve_ai_message_squares, through: :rules

    attribute :name,     :string,  comment: '名称'
    attribute :state,    :string,  comment: '状态'
    attribute :position, :integer, comment: '排序'
    attribute :payload,  :jsonb,   comment: '其他字段'

    def statistic
      {
        rules_count: rules.count,
        rule_records_count: rule_records.count,
        packs_count: packs.count,
        processing_packs_count: packs.where(state: ['pending']).count,
        finished_packs_count: packs.where(state: 'finished').count,
        # pack: packs.group(:state).count,
        messages_count: messages.count,
        read_messages_count: messages.where(is_read: true).count,
        unread_messages_count: messages.where(is_read: [nil, false]).count,
        serve_ai_message_squares_count: serve_ai_message_squares.count
      }
    end

    def latest_send_at
      rules.where.not(latest_send_at: nil).order(latest_send_at: :desc).first&.latest_send_at
    end
  end
end
