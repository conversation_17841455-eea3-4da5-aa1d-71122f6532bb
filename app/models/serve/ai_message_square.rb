class Serve::AiMessageSquare < Serve::AiMessage
  def clone_to_template!(user)
    ai_message_template = user.serve_ai_message_templates.find_or_initialize_by(ref_ai_message_id: self.id)
    ai_message_template.update!(
      name: self.name,
      content: self.content,
      state: 'published',
      pack_id: self.pack_id,
      rule_id: self.rule_id,
      org_id: user.orgs.first&.id,
      creator_id: user.id,
      option: self.option,
      payload: self.payload
    )
    ai_message_template
  end
end
