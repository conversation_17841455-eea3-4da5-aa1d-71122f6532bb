class Serve::RuleRecord < Schedule::RuleRecord
  has_many :packs, -> { current_tanent }, dependent: :destroy
  after_create :generate_pack!

  belongs_to :serve_rule, foreign_key: :rule_id, class_name: 'Serve::Rule'

  scope :current_tanent, -> { Tanent.current ? ransack(serve_rule_tanents_id_eq: Tanent.current&.id).result.distinct : all }

  def period
    Schedule::RuleRecord.where(schedule_uuid: schedule_uuid).count
  end

  # 上一个周期
  def prev
    rule.rule_records
      .ransack(schedule_offset_at_lt: schedule_offset_at)
      .result
      .order(schedule_offset_at: :desc)
      .first
  end

  # 生成消息批次
  def generate_pack!
    # 关联对象发送
    binding.pry
    rule.generate_pack! rule_record: self
  end
end
