
class OrgRelation < ApplicationRecord
  self.track_migration = true
  # 多态关联到source
  belongs_to :source, polymorphic: true
  belongs_to :org

  attribute :mode, :string, comment: '标识'

  # mode的枚举定义
  enum mode: { itself: 'itself', subtree: 'subtree' }
  default_value_for(:mode) { 'itself' }

  # 验证
  validates :source, presence: true
  validates :org, presence: true
  validates :mode, presence: true

  # 验证source和org的组合唯一性
  validates :source_id, uniqueness: {
    scope: [:source_type, :org_id],
    message: '已经与该组织建立了关联'
  }
end
