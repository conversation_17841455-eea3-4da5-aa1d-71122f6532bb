json.partial! 'serve/packs/single', pack: pack
json.extract!(
  pack,
  *pack.class.try(:extra_view_attributes, 'simple'),
  :read_count,
  :unread_count,
  :send_users_count,
  :failed_count,
)

# json.app pack.app, partial: 'apps/single', as: :app
json.rule pack.rule, partial: 'serve/rules/single', as: :rule
json.activity pack.activity, partial: 'serve/activities/single', as: :activity
json.creator pack.creator, partial: 'users/single', as: :user
json.org pack.org, partial: 'orgs/single', as: :org
json.source pack.source&.as_jbuilder_json(partial: 'single')

json.ta_statistics pack.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
