json.extract!(
  pack,
  *pack.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :rule_id,
  :activity_id,
  :create_instance_state,
  :create_instance_timestamp,
  :create_instance_id,
  :type,
  :name,
  :state,
  :seq,
  :period,
  :operate_at,
  :send_at,
  :payload,
  :option,
  :position,
  :creator_id,
  :org_id,
  :org_name,
  :rule_record_id,
  :message_type,
  :activity_name,
  :rule_name,
  :send_user_ids_redis_key,
)
