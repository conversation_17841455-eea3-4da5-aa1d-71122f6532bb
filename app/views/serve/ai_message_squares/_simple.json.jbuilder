json.partial! 'serve/ai_message_squares/single', ai_message_square: ai_message_square
json.extract!(
  ai_message_square,
  *ai_message_square.class.try(:extra_view_attributes, 'simple'),
  :used_count,
)

json.creator ai_message_square.creator, partial: 'users/single', as: :user
json.department_names ai_message_square.creator.department_names
json.rule ai_message_square.rule, partial: 'serve/rules/single', as: :rule
json.has_like ai_message_square.has_like?(@current_user)
json.has_star ai_message_square.has_star?(@current_user)
json.ta_statistics ai_message_square.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
# json.app ai_message_square.app, partial: 'apps/single', as: :app
# json.org ai_message_square.org, partial: 'orgs/single', as: :org
# json.pack ai_message_square.pack, partial: 'serve/packs/single', as: :pack
# json.ref_ai_message ai_message_square.ref_ai_message, partial: 'serve/ai_messages/single', as: :ref_ai_message
