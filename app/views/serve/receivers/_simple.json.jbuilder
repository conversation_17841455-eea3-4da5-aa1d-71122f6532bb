json.partial! 'serve/receivers/single', receiver: receiver
json.extract!(
  receiver,
  *receiver.class.try(:extra_view_attributes, 'simple'),
)

# json.app receiver.app, partial: 'apps/single', as: :app
json.user receiver.user, partial: 'users/single', as: :user
json.pack receiver.pack, partial: 'serve/packs/single', as: :pack

json.ta_statistics receiver.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
