json.partial! 'serve/ai_message_templates/single', ai_message_template: ai_message_template
json.extract!(
  ai_message_template,
  *ai_message_template.class.try(:extra_view_attributes, 'simple'),
)

json.app ai_message_template.app, partial: 'apps/single', as: :app
json.creator ai_message_template.creator, partial: 'users/single', as: :user
# json.org ai_message_template.org, partial: 'orgs/single', as: :org
# json.pack ai_message_template.pack, partial: 'serve/packs/single', as: :pack
json.rule ai_message_template.rule, partial: 'serve/rules/single', as: :rule
json.ref_ai_message ai_message_template.ref_ai_message, partial: 'serve/ai_messages/single', as: :ref_ai_message

json.ta_statistics ai_message_template.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
