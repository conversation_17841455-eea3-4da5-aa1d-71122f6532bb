json.partial! 'serve/origins/single', origin: origin
json.extract!(
  origin,
  *origin.class.try(:extra_view_attributes, 'simple'),
)

# json.app origin.app, partial: 'apps/single', as: :app
# json.org origin.org, partial: 'orgs/single', as: :org
# json.submodule origin.submodule, partial: 'serve/submodules/single', as: :submodule
json.activities_count origin.activities.count

json.ta_statistics origin.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
