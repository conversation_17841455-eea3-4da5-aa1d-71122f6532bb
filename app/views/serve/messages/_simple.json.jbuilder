json.partial! 'serve/messages/single', message: message
json.extract!(
  message,
  *message.class.try(:extra_view_attributes, 'simple'),
  :org_name,
  :rule_name,
)

# json.app message.app, partial: 'apps/single', as: :app
json.user message.user, partial: 'users/single', as: :user
json.sender message.sender, partial: 'users/single', as: :user
# json.notifyable message.notifyable&.as_jbuilder_json(partial: 'single')
json.pack message.pack, partial: 'serve/packs/simple', as: :pack

json.ta_statistics message.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
