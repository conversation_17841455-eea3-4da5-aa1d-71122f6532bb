json.partial! 'serve/ai_messages/single', ai_message: ai_message
json.extract!(
  ai_message,
  *ai_message.class.try(:extra_view_attributes, 'simple'),
)

json.app ai_message.app, partial: 'apps/single', as: :app
json.creator ai_message.creator, partial: 'users/single', as: :user
# json.org ai_message.org, partial: 'orgs/single', as: :org
# json.pack ai_message.pack, partial: 'serve/packs/single', as: :pack
json.rule ai_message.rule, partial: 'serve/rules/single', as: :rule
json.ref_ai_message ai_message.ref_ai_message, partial: 'serve/ai_messages/single', as: :ref_ai_message

json.ta_statistics ai_message.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
