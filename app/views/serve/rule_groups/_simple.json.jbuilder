json.partial! 'serve/rule_groups/single', rule_group: rule_group
json.extract!(
  rule_group,
  *rule_group.class.try(:extra_view_attributes, 'simple'),
  :statistic,
  :latest_send_at
)

# json.app rule_group.app, partial: 'apps/single', as: :app
# json.submodule rule_group.submodule, partial: 'serve/submodules/single', as: :submodule

json.ta_statistics rule_group.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
