json.extract!(
  rule,
  *rule.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :creator_id,
  :type,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :effective_at,
  :invalid_at,
  :name,
  :state,
  :position,
  :options,
  :latest_send_at,
  :batch_no,
  :code,
  :catalog_id,
  :rule_conf,
  :rule_record_type,
  :message_type,
  :rule_group_id,
  :content,
  :description
)
