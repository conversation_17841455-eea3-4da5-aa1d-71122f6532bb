json.partial! 'serve/rules/single', rule: rule
json.extract!(
  rule,
  *rule.class.try(:extra_view_attributes, 'simple'),
  :activity_ids,
  :org_ids,
  :total_packs_count,
  :processing_packs_count,
  :total_rule_records_count,
  :finished_packs_count,
  :orgs,
  :tanent_ids,
)

json.org_names rule.orgs.pluck(:name)

# json.app rule.app, partial: 'apps/single', as: :app
# json.creator rule.creator, partial: 'users/single', as: :creator
json.catalog rule.catalog, partial: 'serve/catalogs/single', as: :catalog

json.ta_statistics rule.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
