json.partial! 'iest/ai/chat/mentions/single', mention: mention
json.extract!(
  mention,
  *mention.class.try(:extra_view_attributes, 'simple'),
)

json.conversation mention.conversation, partial: 'iest/ai/chat/conversations/single', as: :conversation
json.app mention.app, partial: 'apps/single', as: :app

json.ta_statistics mention.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
