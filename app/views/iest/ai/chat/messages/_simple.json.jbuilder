json.partial! 'iest/ai/chat/messages/single', message: message
json.extract!(
  message,
  *message.class.try(:extra_view_attributes, 'simple'),
)

# json.conversation message.conversation, partial: 'iest/ai/chat/conversations/single', as: :conversation
json.mentions message.mentions, partial: 'iest/ai/chat/mentions/single', as: :mention

json.ta_statistics message.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
