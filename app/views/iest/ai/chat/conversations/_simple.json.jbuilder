json.partial! 'iest/ai/chat/conversations/single', conversation: conversation
json.extract!(
  conversation,
  *conversation.class.try(:extra_view_attributes, 'simple'),
)

json.app conversation.app, partial: 'apps/single', as: :app
json.current_intent conversation.current_intent, partial: 'iest/ai/chat/intents/single', as: :current_intent

json.ta_statistics conversation.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
