json.partial! 'iest/ai/chat/mention_versions/single', mention_version: mention_version
json.extract!(
  mention_version,
  *mention_version.class.try(:extra_view_attributes, 'simple'),
)

# json.mention mention_version.mention, partial: 'iest/ai/chat/mentions/single', as: :mention
# json.message mention_version.message, partial: 'iest/ai/chat/messages/single', as: :message
# json.conversation mention_version.conversation, partial: 'iest/ai/chat/conversations/single', as: :conversation

json.ta_statistics mention_version.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
