json.partial! 'iest/paperworks/single', paperwork: paperwork
json.extract!(
  paperwork,
  *paperwork.class.try(:extra_view_attributes, 'simple'),
)

json.user paperwork.user, partial: 'users/single', as: :user
# json.app paperwork.app, partial: 'apps/single', as: :app
json.org paperwork.org, partial: 'orgs/single', as: :org
json.paperwork_result_count paperwork.paperwork_results.count

json.ta_statistics paperwork.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
