class ::Iest::Ai::Chat::User::MentionVersionsController < SimpleController::BaseController
  defaults(
    resource_class: Iest::Ai::Chat::MentionVersion,
    collection_name: 'mention_versions',
    instance_name: 'mention_version',
    view_path: 'iest/ai/chat/mention_versions',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :conversation, collection_name: :iest_ai_chat_conversations

  protected

  def begin_of_association_chain
    current_app
  end

  # private

  # def mention_version_params
  #   params.require(:mention_version).permit(
  #     *resource_class.try(:extra_permitted_attributes),
  #     :mention_id,
  #     :message_id,
  #     :conversation_id,
  #     payload: {},
  #   )
  # end
end
