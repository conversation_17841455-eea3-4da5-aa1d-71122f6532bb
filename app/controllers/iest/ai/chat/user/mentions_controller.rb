class ::Iest::Ai::Chat::User::MentionsController < SimpleController::BaseController
  defaults(
    resource_class: Iest::Ai::Chat::Mention,
    collection_name: 'mentions',
    instance_name: 'mention',
    view_path: 'iest/ai/chat/mentions',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :conversation, collection_name: :iest_ai_chat_conversations

  protected

  def begin_of_association_chain
    current_app
  end

  private

  def mention_params
    params.require(:mention).permit(
      *resource_class.try(:extra_permitted_attributes),
      :mentionable_type,
      :mentionable_id,
      :conversation_id,
      :app_id,
      :type,
      :name,
      :state,
      :self_intent,
      :aasm_state_event,
      :aasm_state_event_later,
      payload: {},
      renew_version: {},
    ).merge(
      current_user: current_user,
    )
  end
end
