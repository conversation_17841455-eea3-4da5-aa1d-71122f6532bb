class ::Iest::Ai::Chat::User::ConversationsController < SimpleController::BaseController
  defaults(
    resource_class: Iest::Ai::Chat::Conversation,
    collection_name: 'conversations',
    instance_name: 'conversation',
    view_path: 'iest/ai/chat/conversations',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :iest_ai_chat_conversations
  end

  private

  def create_conversation_params
    conversation_params.merge(
      user: current_user,
      tanent: Tanent.current,
    )
  end

  def conversation_params
    params.fetch(:conversation, {}).permit(
      *resource_class.try(:extra_permitted_attributes),
      # :current_intent_id,
      :name,
    )
  end
end
