class ::Iest::Ai::Chat::User::MessagesController < SimpleController::BaseController
  defaults(
    resource_class: Iest::Ai::Chat::Message,
    collection_name: 'messages',
    instance_name: 'message',
    view_path: 'iest/ai/chat/messages',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :conversation, collection_name: :iest_ai_chat_conversations

  protected

  def begin_of_association_chain
    current_app
  end

  private

  def message_params
    params.require(:message).permit(
      *resource_class.try(:extra_permitted_attributes),
      :conversation_id,
      :type,
      :previous_content,
      :content,
      :result_type,
      suggestions: {},
    )
  end
end
