class Iest::AiNotify::Manage::MessagesController < SimpleController::BaseController
  auth_action :user
  permit_action :serve_admin, :serve_manage

  def create
    message = Iest::Ai::Notify::Message.create(
      message_params[:sentence],
      history: message_params[:history],
      meta: message_params[:meta],
      app: current_app,
    )

    render json: message, status: 201
  end

  private

  def message_params
    params.require(:message).permit(
      :sentence,
      history: [],
      meta: {},
    )
  end
end
