class Iest::Manage::OrgsController < SimpleController::BaseController
  defaults(
    resource_class: Org,
    collection_name: 'orgs',
    instance_name: 'org',
    view_path: 'orgs',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    Tanent.current
  end

  def after_association_chain(association)
    unless current_user.has_role?(:serve_admin)
      association = association.ransack(self_and_ancestors_id_in: current_user.orgs).result
    end
    association.order(position: :asc)
  end
end
