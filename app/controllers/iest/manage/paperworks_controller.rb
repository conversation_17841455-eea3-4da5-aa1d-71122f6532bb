class ::Iest::Manage::PaperworksController < SimpleController::BaseController
  defaults(
    resource_class: Iest::Paperwork,
    collection_name: 'paperworks',
    instance_name: 'paperwork',
    view_path: 'iest/paperworks',
  )

  auth_action :user

  permit_action :iest_admin, :iest_manage

  private

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :iest_paperworks
  end

  def after_association_chain(association)
    association.ransack(org_self_and_ancestors_id_in: current_user.orgs.pluck(:id)).result
  end

  def create_paperwork_params
    paperwork_params.merge(
      user: current_user
    )
  end

  def paperwork_params
    params.require(:paperwork).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      # :operate_at,
      # :state,
      # :app_id,
      # :user_id,
      :org_id,
      attachment: {},
      # response: {},
    )
  end
end
