class Iest::Manage::AppsController < SimpleController::BaseController
  defaults(
    resource_class: App,
    collection_name: 'apps',
    instance_name: 'app',
    view_path: 'apps',
  )

  include Com::Controller::TaStatistic

  auth_action :user
  permit_action :serve_admin, :serve_manage, :iest_manage, :iest_admin

  def ta_collection_statistic
    stat_condition = Com::Attr::Stat::Collection.new params.to_unsafe_h[:stat_condition]
    cache_key = "ta_collection_statistic/#{collection.model_name.name}/#{Digest::MD5.hexdigest(params.to_unsafe_h[:stat_condition].to_json)}"

    if !params[:force_refresh].present? && Rails.cache.exist?(cache_key)
      stat_result = Rails.cache.read(cache_key)
    else
      stat_result = collection.ta_statistic(stat_condition)
      Rails.cache.write(cache_key, stat_result, expires_in: 5.minutes)
    end

    render json: stat_result, status: 201
  end

  def ta_resource_statistic
    stat_condition = Com::Attr::Stat::Resource.new params.to_unsafe_h[:stat_condition]
    cache_key = "ta_resource_statistic/#{resource.class.model_name.name}/#{resource.id}/#{Digest::MD5.hexdigest(params.to_unsafe_h[:stat_condition].to_json)}"

    if !params[:force_refresh].present? && Rails.cache.exist?(cache_key)
      stat_result = Rails.cache.read(cache_key)
    else
      stat_result = resource.ta_statistic(stat_condition)
      Rails.cache.write(cache_key, stat_result, expires_in: 30.minutes)
    end

    render json: stat_result, status: 201
  end
end
