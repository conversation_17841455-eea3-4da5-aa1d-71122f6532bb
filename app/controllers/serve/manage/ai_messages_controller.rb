class Serve::Manage::AiMessagesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::AiMessage,
    collection_name: 'ai_messages',
    instance_name: 'ai_message',
    view_path: 'serve/ai_messages',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_user.has_role?(:serve_admin) ? current_app : current_user
  end

  def method_for_association_chain
    :serve_ai_messages
  end

  private

  def update_ai_message_params
    params.require(:ai_message).permit(
      *resource_class.try(:extra_permitted_attributes),
      :pack_id,
      :rule_id,
      :ref_ai_message_id,
      :type,
      :seq,
      :name,
      :state,
      :content,
      option: {},
      payload: {},
    )
  end

  def create_ai_message_params
    update_ai_message_params.merge(
      creator: current_user,
    )
  end
end
