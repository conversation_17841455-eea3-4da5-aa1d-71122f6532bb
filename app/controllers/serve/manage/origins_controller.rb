class Serve::Manage::OriginsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Origin,
    collection_name: 'origins',
    instance_name: 'origin',
    view_path: 'serve/origins',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_origins
  end

  def after_association_chain(association)
    association.order(position: :asc)
  end

  private

  def origin_params
    params.require(:origin).permit(
      *resource_class.try(:extra_permitted_attributes),
      :org_id,
      :model_flag,
      :type,
      :name,
      :state,
      :code,
      :mode,
      :position,
      :submodule_id,
      :latest_send_at,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
    )
  end
end
