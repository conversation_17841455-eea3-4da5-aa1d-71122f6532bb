class Serve::Manage::SendUsersController < SimpleController::BaseController
  defaults(
    resource_class: User,
    collection_name: 'users',
    instance_name: 'user',
    view_path: 'users',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :pack, collection_name: :serve_ai_packs

  def index
    association = end_of_association_chain
    # 应用ransack过滤
    association = ransack_association(association, params[:q]) unless self.class.instance_variable_get(:@ransack_off) || params[:q].blank?
    association = ransack_association(association, params[:sub_q]) unless self.class.instance_variable_get(:@ransack_off) || params[:sub_q].blank?
    # 可选：应用排序
    association = association.order(id: :desc) if association.respond_to?(:order) && !self.class.instance_variable_get(:@order_off)
    # 应用分页 - 从params[:q]中获取分页参数
    if params[:q].present? && params[:q][:page].present? && params[:q][:per_page].present?
      association = association.paginate(page: params[:q][:page], per_page: params[:q][:per_page])
    end
    @users = association.pluck(:id).uniq

    # 返回分页信息
    if association.respond_to?(:total_entries)
      render json: {
        data: @users,
        total_count: association.total_entries,
        current_page: params[:q][:page].to_i,
        per_page: params[:q][:per_page].to_i,
        total_pages: (association.total_entries.to_f / params[:q][:per_page].to_i).ceil
      }
    else
      render json: @users
    end
  end

  protected

  def begin_of_association_chain
    if current_user.has_role?(:serve_admin) || current_user.has_role?(:serve_manage)
      current_app
    else
      current_user
    end
  end

  def method_for_association_chain
    :send_users
  end
end
