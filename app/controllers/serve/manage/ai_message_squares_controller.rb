class Serve::Manage::AiMessageSquaresController < SimpleController::BaseController
  defaults(
    resource_class: Serve::AiMessageSquare,
    collection_name: 'ai_message_squares',
    instance_name: 'ai_message_square',
    view_path: 'serve/ai_message_squares',
    order_off: true,
  )

  include Favor::Controller::Markable
  auth_action :user
  permit_action :serve_admin, :serve_manage

  def clone_to_template
    @ai_message_template = resource.clone_to_template!(current_user)
    render json: @ai_message_template, status: 201
  end

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_ai_message_squares
  end
end
