class Serve::Manage::RuleGroupsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::RuleGroup,
    collection_name: 'rule_groups',
    instance_name: 'rule_group',
    view_path: 'serve/rule_groups',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_rule_groups
  end

  def after_association_chain(association)
    association.order(position: :asc)
  end

  private

  def rule_group_params
    params.require(:rule_group).permit(
      *resource_class.try(:extra_permitted_attributes),
      :submodule_id,
      :name,
      :state,
      :position,
      payload: {},
    )
  end
end
