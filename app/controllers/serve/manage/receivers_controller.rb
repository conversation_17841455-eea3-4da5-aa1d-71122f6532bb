class Serve::Manage::ReceiversController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Receiver,
    collection_name: 'receivers',
    instance_name: 'receiver',
    view_path: 'serve/receivers',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :pack, collection_name: :serve_ai_packs

  protected

  def begin_of_association_chain
    current_user.has_role?(:serve_admin) ? current_app : current_user
  end

  private

  def receiver_params
    params.require(:receiver).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_id,
      :pack_id,
      :state,
      payload: {},
    )
  end
end
