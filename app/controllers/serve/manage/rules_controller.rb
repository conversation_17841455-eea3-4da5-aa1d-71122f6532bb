class Serve::Manage::RulesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Rule,
    collection_name: 'rules',
    instance_name: 'rule',
    view_path: 'serve/rules',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_user.has_role?(:serve_admin) || params[:action] == 'create' ? current_app : current_user
  end

  def after_association_chain(association)
    association.unscope(:order).order(position: :asc)
  end

  def method_for_association_chain
    :serve_rules
  end

  private

  def create_rule_params
    rule_params.merge(
      tanents: [Tanent.current],
    )
  end

  def rule_params
    params.require(:rule).permit(
      *resource_class.try(:extra_permitted_attributes),
      :creator_id,
      :type,
      :model_flag,
      :effective_at,
      :invalid_at,
      :name,
      :state,
      :position,
      :batch_no,
      :code,
      :latest_send_at,
      :catalog_id,
      :rule_group_id,
      :rule_record_type,
      :message_type,
      :description,
      content: {},
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      options: {},
      rule_conf: {},
      org_ids: [],
      activity_ids: [],
      tanent_ids: [],
      rule_items_attributes: [:id, :name, :state, :type, :uuid, :mode, :_destroy, option: {}, activity_ids: []],
      message_templates_attributes: [:id, :name, :state, :type, :rule_item_id, :_destroy, option: {}, payload: {}],
      org_relations_attributes: [:id, :org_id, :mode, :_destroy],
    )
  end
end
