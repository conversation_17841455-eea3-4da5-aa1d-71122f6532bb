class Serve::Manage::RuleItemsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::RuleItem,
    collection_name: 'rule_items',
    instance_name: 'rule_item',
    view_path: 'serve/rule_items',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_rule_items
  end

  private

  def rule_item_params
    params.require(:rule_item).permit(
      *resource_class.try(:extra_permitted_attributes),
      :rule_id,
      :name,
      :state,
      option: {},
      payload: {},
    )
  end
end
