class Serve::Manage::PacksController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Pack,
    collection_name: 'packs',
    instance_name: 'pack',
    view_path: 'serve/packs',
    distinct_off: true,
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_user.has_role?(:serve_admin) || params[:action] == 'create' ? current_app : current_user
  end

  def method_for_association_chain
    :serve_packs
  end

  def after_association_chain(association)
    if current_user.has_role?(:serve_manage)
      association.
        select('DISTINCT ON (serve_packs.id) serve_packs.*, org_hierarchies.generations AS org_hierarchy').
        reorder(nil).
        order('serve_packs.id, org_hierarchies.generations ASC')
    else
      association
    end
  end
  private

  def create_pack_params
    pack_params.merge(
      tanents: [Tanent.current],
      creator: current_user,
    )
  end

  def pack_params
    params.require(:pack).permit(
      *resource_class.try(:extra_permitted_attributes),
      :rule_id,
      :activity_id,
      :create_instance_state,
      :create_instance_timestamp,
      :type,
      :name,
      :state,
      :seq,
      :period,
      :operate_at,
      :send_at,
      :position,
      :content,
      :rule_record_id,
      :message_type,
      :send_user_ids_redis_key,
      :send_user_ids_redis_key_refresh,
      payload: {},
      option: {},
      send_user_ids: [],
    )
  end
end
