class Serve::Manage::AiMessageTemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::AiMessageTemplate,
    collection_name: 'ai_message_templates',
    instance_name: 'ai_message_template',
    view_path: 'serve/ai_message_templates',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :serve_ai_message_templates
  end

  private

  def update_ai_message_template_params
    params.require(:ai_message_template).permit(
      *resource_class.try(:extra_permitted_attributes),
      :pack_id,
      :rule_id,
      :ref_ai_message_id,
      :type,
      :seq,
      :name,
      :state,
      :content,
      option: {},
      payload: {},
    )
  end

  def create_ai_message_template_params
    update_ai_message_template_params.merge(
      creator: current_user,
    )
  end
end
