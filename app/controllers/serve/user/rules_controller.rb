class Serve::User::RulesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Rule,
    collection_name: 'rules',
    instance_name: 'rule',
    view_path: 'serve/rules',
  )
  auth_action :user

  def generate_content_by_template_prompt
    contents = resource.generate_content_by_template_prompt(prompt: params[:prompt])
    render json: { contents: contents }, status: 201
  end

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :serve_rules
  end
end
