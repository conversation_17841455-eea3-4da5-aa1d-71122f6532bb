class Serve::User::PacksController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Pack,
    collection_name: 'packs',
    instance_name: 'pack',
    view_path: 'serve/packs',
  )
  auth_action :user

  def refresh_contents_by_rule
    # 接收mode参数，默认为nil（使用标准模式）
    contents = resource.refresh_contents_by_rule(
      prompt: params[:prompt],
      mode: params[:mode]
    )

    # 获取DeepSeek模式信息和生成统计
    payload_info = resource.payload || {}
    deepseek_mode_info = payload_info['deepseek_mode_info'] || resource.rule.get_deepseek_mode_info

    render json: {
      contents: contents,
      deepseek_mode_info: deepseek_mode_info,
      generation_time: payload_info['generation_time'],
      generated_at: payload_info['generated_at']
    }, status: 201
  end

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :serve_packs
  end
end
