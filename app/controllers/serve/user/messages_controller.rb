class Serve::User::MessagesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Message,
    collection_name: 'messages',
    instance_name: 'message',
    view_path: 'serve/messages',
  )

  # auth_action :user

  def show
    show! do
      resource.update(is_read: true) if resource && !resource.is_read
      resource.activity&.increment!(:views_count)
    end
  end

  protected

  # def begin_of_association_chain
  #   current_user
  # end

  # def after_association_chain(association)
  #   association.current_tanent
  # end

  # def method_for_association_chain
  #   :serve_messages
  # end

  def resource
    # get_resource_ivar || set_resource_ivar(after_of_association_chain.find_by!(seq: params[:id]))
    @message = Serve::Message.find_by!(seq: params[:id])
  end
end
