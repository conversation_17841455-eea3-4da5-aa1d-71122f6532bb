FactoryBot.define do
  factory :serve_message_template, class: 'Serve::MessageTemplate' do
    name { '测试模版' }
    state { 'used' }
    option {
      {
        image: {
          enabled: true,
          files: [
            {
              position: 'top',
              url: 'https://www.tallty1.com'
            },
            {
              position: 'bottom',
              url: 'https://www.tallty2.com'
            }
          ]
        },
        card_content: {
          enabled: true,
          templates: [
            {
              title: '廉洁提醒消息',
              content: '您有一条关于【%{user_name}】的提醒，请查收。',
              scopes: [
                {
                  key: 'user',
                  val: 'user_name',
                  method: 'name'
                }
              ]
            }
          ]
        },
        content: {
          enabled: true,
          templates: [
            {
              title: '廉洁提醒消息',
              content: '您有一条关于【%{user_name}】的提醒，请查收。',
              scopes: [
                {
                  key: 'user',
                  val: 'user_name',
                  method: 'name'
                }
              ]
            }
          ]
        }
      }
    }
  end
end
