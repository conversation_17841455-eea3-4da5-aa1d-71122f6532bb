default: &default
  adapter: <%= ENV["DATABASE_ADAPTER"] %>
  pool: <%= ENV["DATABASE_POOL"] %>
  encoding: <%= ENV["DATABASE_ENCODING"] %>
  username: <%= <PERSON>NV["DATABASE_USERNAME"] %>
  password: <%= ENV["DATABASE_PASSWORD"] %>
  host: <%= ENV["DATABASE_HOST"] %>
  port: <%= ENV["DATABASE_PORT"] %>

development:
  <<: *default
  database: <%= ENV["DATABASE_NAME"] %>

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: <%= ENV["DATABASE_NAME"] %>

production:
  <<: *default
  database: <%= ENV["DATABASE_NAME"] %>
