require 'swagger_helper'

RSpec.describe 'iest/manage/apps', type: :request, capture_examples: true, tags: ["iest manage"] do
  app_ref = {
    type: :object, properties: {
      app: {
        type: :object, properties: {
          code: { type: :string, description: '应用标识' },
          name: { type: :string, description: '应用的名称' },
          settings: { type: :jsonb, description: '配置信息' }
        }
      }
    }
  }
  app_value = FactoryBot.attributes_for(:app)

  before :each do
  end
end
