require 'swagger_helper'

RSpec.describe 'iest/manage/tags', type: :request, capture_examples: true, tags: ["iest manage"] do
  tag_ref = {
    type: :object, properties: {
      tag: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          org_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '标签名称' },
          color: { type: :string, description: '标签颜色' }
        }
      }
    }
  }
  # tag_value = FactoryBot.attributes_for(:res_tag)

  before :each do
  end

  # path '/iest/manage/tags' do

  #   get(summary: 'list tags') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     response(200, description: 'successful') do
  #       it {
  #         body = JSON.parse(response.body)
  #         expect(body['records'].count).to eq @res_tag_count
  #       }
  #     end
  #   end
  # end

  # path '/iest/manage/tags/{id}' do
  #   parameter 'id', in: :path, type: :string

  #   get(summary: 'show tag') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     response(200, description: 'successful') do
  #       let(:id) { @res_tags.first.id }
  #       it {
  #         body = JSON.parse(response.body)
  #         tag = @res_tags.first
  #         expect(body['app_id']).to eq tag.app_id
  #         expect(body['org_id']).to eq tag.org_id
  #         expect(body['model_flag']).to eq tag.model_flag
  #         expect(body['model_payload']).to eq tag.model_payload
  #         expect(body['model_payload_summary']).to eq tag.model_payload_summary
  #         expect(body['model_detail']).to eq tag.model_detail
  #         expect(body['name']).to eq tag.name
  #         expect(body['color']).to eq tag.color
  #       }
  #     end
  #   end
  # end
end
