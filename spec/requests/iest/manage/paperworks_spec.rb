require 'swagger_helper'

RSpec.describe '/iest/manage/paperworks', type: :request, capture_examples: true, tags: [" iest manage"] do
  paperwork_ref = {
    type: :object, properties: {
      paperwork: {
        type: :object, properties: {
          name: { type: :string, description: '名称' },
          operate_at: { type: :datetime, description: '操作时间' },
          state: { type: :string, description: '状态' },
          attachment: { type: :jsonb, description: '附件' },
          response: { type: :jsonb, description: '响应' },
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          org_id: { type: :integer, description: '' }
        }
      }
    }
  }
  paperwork_value = FactoryBot.attributes_for(:iest_paperwork)

  before :each do
    @iest_paperwork_count = 5
    @iest_paperworks = FactoryBot.create_list(:iest_paperwork, @iest_paperwork_count, user: @user)
  end

  path '/iest/manage/paperworks' do
    get(summary: 'list paperworks') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @iest_paperwork_count
        }
      end
    end

    post(summary: 'create paperwork') do
      produces 'application/json'
      consumes 'application/json'
      parameter :paperwork, in: :body, schema: paperwork_ref
      response(201, description: 'successful') do
        let(:paperwork) do
          { paperwork: paperwork_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['name']).to eq paperwork_value[:name]
          expect(body['operate_at']).to eq paperwork_value[:operate_at]
          expect(body['state']).to eq paperwork_value[:state]
          expect(body['attachment']).to eq paperwork_value[:attachment]
          expect(body['response']).to eq paperwork_value[:response]
          expect(body['app_id']).to eq @user.app.id
          expect(body['user_id']).to eq @user.id
          expect(body['org_id']).to eq paperwork_value[:org_id]
        }
      end
    end
  end

  path '/iest/manage/paperworks/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show paperwork') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @iest_paperworks.first.id }
        it {
          body = JSON.parse(response.body)
          paperwork = @iest_paperworks.first
          expect(body['name']).to eq paperwork.name
          expect(body['operate_at']).to eq paperwork.operate_at
          expect(body['state']).to eq paperwork.state
          expect(body['attachment']).to eq paperwork.attachment
          expect(body['response']).to eq paperwork.response
          expect(body['app_id']).to eq paperwork.app_id
          expect(body['user_id']).to eq paperwork.user_id
          expect(body['org_id']).to eq paperwork.org_id
        }
      end
    end

    patch(summary: 'update paperwork') do
      produces 'application/json'
      consumes 'application/json'
      parameter :paperwork, in: :body, schema: paperwork_ref
      response(201, description: 'successful') do
        let(:id) { @iest_paperworks.first.id }
        let(:paperwork) do
          { paperwork: paperwork_value }
        end
      end
    end

    delete(summary: 'delete paperwork') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @iest_paperworks.first.id }
        it {
          expect(Iest::Paperwork.count).to eq(@iest_paperwork_count-1)
        }
      end
    end
  end
end
