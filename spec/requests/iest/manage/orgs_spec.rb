require 'swagger_helper'

RSpec.describe 'iest/manage/orgs', type: :request, capture_examples: true, tags: ["iest manage"] do
  org_ref = {
    type: :object, properties: {
      org: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          org_identity_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          payload: { type: :jsonb, description: 'payload payload存储的字段' },
          payload_summary: { type: :jsonb, description: 'payload summary存储的字段' },
          parent_id: { type: :integer, description: 'closure tree parent_id' },
          code: { type: :string, description: '组织标识' },
          name: { type: :string, description: '组织名称' },
          short_name: { type: :string, description: '组织简称' },
          type: { type: :string, description: 'STI类型，可以是集团，或者在某些时候可能是学校这样的类型' },
          position: { type: :integer, description: '排序' },
          region_area_id: { type: :integer, description: '' },
          province: { type: :string, description: '省' },
          city: { type: :string, description: '市' },
          district: { type: :string, description: '区' }
        }
      }
    }
  }
  org_value = FactoryBot.attributes_for(:org)

  before :each do
  end

  # path '/iest/manage/orgs' do

  #   get(summary: 'list orgs') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     response(200, description: 'successful') do
  #       it {
  #         body = JSON.parse(response.body)
  #         expect(body['records'].count).to eq @org_count
  #       }
  #     end
  #   end
  # end

  # path '/iest/manage/orgs/{id}' do
  #   parameter 'id', in: :path, type: :string

  #   get(summary: 'show org') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     response(200, description: 'successful') do
  #       let(:id) { @orgs.first.id }
  #       it {
  #         body = JSON.parse(response.body)
  #         org = @orgs.first
  #         expect(body['app_id']).to eq org.app_id
  #         expect(body['org_identity_id']).to eq org.org_identity_id
  #         expect(body['model_flag']).to eq org.model_flag
  #         expect(body['model_payload']).to eq org.model_payload
  #         expect(body['model_payload_summary']).to eq org.model_payload_summary
  #         expect(body['model_detail']).to eq org.model_detail
  #         expect(body['payload']).to eq org.payload
  #         expect(body['payload_summary']).to eq org.payload_summary
  #         expect(body['parent_id']).to eq org.parent_id
  #         expect(body['code']).to eq org.code
  #         expect(body['name']).to eq org.name
  #         expect(body['short_name']).to eq org.short_name
  #         expect(body['type']).to eq org.type
  #         expect(body['position']).to eq org.position
  #         expect(body['region_area_id']).to eq org.region_area_id
  #         expect(body['province']).to eq org.province
  #         expect(body['city']).to eq org.city
  #         expect(body['district']).to eq org.district
  #       }
  #     end
  #   end
  # end
end
