require 'swagger_helper'

RSpec.describe '/iest/ai/chat/user/mention_versions', type: :request, capture_examples: true, tags: [" iest ai chat user"] do
  mention_version_ref = {
    type: :object, properties: {
      mention_version: {
        type: :object, properties: {
          mention_id: { type: :integer, description: '' },
          message_id: { type: :integer, description: '' },
          conversation_id: { type: :integer, description: '' },
          payload: { type: :jsonb, description: '数据' }
        }
      }
    }
  }
  # mention_version_value = FactoryBot.attributes_for(:iest_ai_chat_mention_version)

  before :each do
    # @iest_ai_chat_mention_version_count = 5
    # @iest_ai_chat_mention_versions = FactoryBot.create_list(:iest_ai_chat_mention_version, @iest_ai_chat_mention_version_count)
  end
end
