require 'swagger_helper'

RSpec.describe '/iest/ai/chat/user/messages', type: :request, capture_examples: true, tags: [" iest ai chat user"] do
  message_ref = {
    type: :object, properties: {
      message: {
        type: :object, properties: {
          conversation_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          previous_content: { type: :text, description: '前一句话的内容' },
          content: { type: :text, description: '内容' },
          result_type: { type: :string, description: '处理结果标识' },
          suggestions: { type: :jsonb, description: '提示建议' }
        }
      }
    }
  }
  message_value = FactoryBot.attributes_for(:iest_ai_chat_message)

  before :each do
    @user.add_role(:serve_admin)

    # @iest_ai_chat_message_count = 5
    # @iest_ai_chat_messages = FactoryBot.create_list(:iest_ai_chat_message, @iest_ai_chat_message_count)
  end

  path '/iest/ai/chat/user/conversations/{conversation_id}/messages' do
    parameter 'conversation_id', in: :path, type: :string

    get(summary: 'list messages') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @iest_ai_chat_message_count
        }
      end
    end

    post(summary: 'create message') do
      produces 'application/json'
      consumes 'application/json'
      parameter :message, in: :body, schema: message_ref
      response(201, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:message) do
          { message: message_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['conversation_id']).to eq @iest_ai_chat_conversation.id
          expect(body['type']).to eq message_value[:type]
          expect(body['previous_content']).to eq message_value[:previous_content]
          expect(body['content']).to eq message_value[:content]
          expect(body['result_type']).to eq message_value[:result_type]
          expect(body['suggestions']).to eq message_value[:suggestions]
        }
      end
    end
  end

  path '/iest/ai/chat/user/conversations/{conversation_id}/messages/{id}' do
    parameter 'conversation_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show message') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:id) { @iest_ai_chat_messages.first.id }
        it {
          body = JSON.parse(response.body)
          message = @iest_ai_chat_messages.first
          expect(body['conversation_id']).to eq message.conversation_id
          expect(body['type']).to eq message.type
          expect(body['previous_content']).to eq message.previous_content
          expect(body['content']).to eq message.content
          expect(body['result_type']).to eq message.result_type
          expect(body['suggestions']).to eq message.suggestions
        }
      end
    end

    patch(summary: 'update message') do
      produces 'application/json'
      consumes 'application/json'
      parameter :message, in: :body, schema: message_ref
      response(201, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:id) { @iest_ai_chat_messages.first.id }
        let(:message) do
          { message: message_value }
        end
      end
    end

    delete(summary: 'delete message') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:id) { @iest_ai_chat_messages.first.id }
        it {
          expect(Iest::Ai::Chat::Message.count).to eq(@iest_ai_chat_message_count-1)
        }
      end
    end
  end
end
