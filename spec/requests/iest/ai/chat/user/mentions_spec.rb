require 'swagger_helper'

RSpec.describe '/iest/ai/chat/user/mentions', type: :request, capture_examples: true, tags: [" iest ai chat user"] do
  mention_ref = {
    type: :object, properties: {
      mention: {
        type: :object, properties: {
          mentionable_type: { type: :string, description: '' },
          mentionable_id: { type: :integer, description: '' },
          conversation_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          payload: { type: :jsonb, description: '数据' },
          self_intent: { type: :string, description: '意图标识，用于归集相同意图的消息下的 mentions' }
        }
      }
    }
  }
  mention_value = FactoryBot.attributes_for(:iest_ai_chat_mention)

  before :each do
    @user.add_role(:serve_admin)

    # @iest_ai_chat_mention_count = 5
    # @iest_ai_chat_mentions = FactoryBot.create_list(:iest_ai_chat_mention, @iest_ai_chat_mention_count)
  end

  path '/iest/ai/chat/user/conversations/{conversation_id}/mentions' do
    parameter 'conversation_id', in: :path, type: :string

    get(summary: 'list mentions') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @iest_ai_chat_mention_count
        }
      end
    end

    post(summary: 'create mention') do
      produces 'application/json'
      consumes 'application/json'
      parameter :mention, in: :body, schema: mention_ref
      response(201, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:mention) do
          { mention: mention_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['mentionable_type']).to eq mention_value[:mentionable_type]
          expect(body['mentionable_id']).to eq mention_value[:mentionable_id]
          expect(body['conversation_id']).to eq @iest_ai_chat_conversation.id
          expect(body['app_id']).to eq @app.id
          expect(body['type']).to eq mention_value[:type]
          expect(body['name']).to eq mention_value[:name]
          expect(body['state']).to eq 'pending'
          expect(body['payload']).to eq mention_value[:payload]
          expect(body['self_intent']).to eq mention_value[:self_intent]
        }
      end
    end
  end

  path '/iest/ai/chat/user/conversations/{conversation_id}/mentions/{id}' do
    parameter 'conversation_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show mention') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:id) { @iest_ai_chat_mentions.first.id }
        it {
          body = JSON.parse(response.body)
          mention = @iest_ai_chat_mentions.first
          expect(body['mentionable_type']).to eq mention.mentionable_type
          expect(body['mentionable_id']).to eq mention.mentionable_id
          expect(body['conversation_id']).to eq mention.conversation_id
          expect(body['app_id']).to eq mention.app_id
          expect(body['type']).to eq mention.type
          expect(body['name']).to eq mention.name
          expect(body['state']).to eq mention.state
          expect(body['payload']).to eq mention.payload
          expect(body['self_intent']).to eq mention.self_intent
        }
      end
    end

    patch(summary: 'update mention') do
      produces 'application/json'
      consumes 'application/json'
      parameter :mention, in: :body, schema: mention_ref
      response(201, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:id) { @iest_ai_chat_mentions.first.id }
        let(:mention) do
          { mention: mention_value }
        end
      end
    end

    delete(summary: 'delete mention') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:conversation_id) { @iest_ai_chat_conversation.id }
        let(:id) { @iest_ai_chat_mentions.first.id }
        it {
          expect(Iest::Ai::Chat::Mention.count).to eq(@iest_ai_chat_mention_count-1)
        }
      end
    end
  end
end
