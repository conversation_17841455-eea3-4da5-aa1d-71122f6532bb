require 'swagger_helper'

RSpec.describe '/iest/ai/chat/user/conversations', type: :request, capture_examples: true, tags: [" iest ai chat user"] do
  conversation_ref = {
    type: :object, properties: {
      conversation: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          current_intent_id: { type: :integer, description: '' },
          name: { type: :string, description: '对话名称' }
        }
      }
    }
  }
  conversation_value = FactoryBot.attributes_for(:iest_ai_chat_conversation)

  before :each do
    @user.add_role(:serve_admin)
    # @iest_ai_chat_conversation_count = 5
    # @iest_ai_chat_conversations = FactoryBot.create_list(:iest_ai_chat_conversation, @iest_ai_chat_conversation_count)
  end

  path '/iest/ai/chat/user/conversations' do
    get(summary: 'list conversations') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @iest_ai_chat_conversation_count
        }
      end
    end

    post(summary: 'create conversation') do
      produces 'application/json'
      consumes 'application/json'
      parameter :conversation, in: :body, schema: conversation_ref
      response(201, description: 'successful') do
        let(:conversation) do
          { conversation: conversation_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          # expect(body['current_intent_id']).to eq conversation_value[:current_intent_id]
          expect(body['name']).to eq conversation_value[:name]
        }
      end
    end
  end

  path '/iest/ai/chat/user/conversations/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show conversation') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @iest_ai_chat_conversations.first.id }
        it {
          body = JSON.parse(response.body)
          conversation = @iest_ai_chat_conversations.first
          expect(body['app_id']).to eq conversation.app_id
          expect(body['current_intent_id']).to eq conversation.current_intent_id
          expect(body['name']).to eq conversation.name
        }
      end
    end

    patch(summary: 'update conversation') do
      produces 'application/json'
      consumes 'application/json'
      parameter :conversation, in: :body, schema: conversation_ref
      response(201, description: 'successful') do
        let(:id) { @iest_ai_chat_conversations.first.id }
        let(:conversation) do
          { conversation: conversation_value }
        end
      end
    end

    delete(summary: 'delete conversation') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @iest_ai_chat_conversations.first.id }
        it {
          expect(Iest::Ai::Chat::Conversation.count).to eq(@iest_ai_chat_conversation_count-1)
        }
      end
    end
  end
end
