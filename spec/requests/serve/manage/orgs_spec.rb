require 'swagger_helper'

RSpec.describe 'serve/manage/orgs', type: :request, capture_examples: true, tags: ["serve manage"] do
  org_ref = {
    type: :object, properties: {
      org: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          org_identity_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          payload: { type: :jsonb, description: 'payload payload存储的字段' },
          payload_summary: { type: :jsonb, description: 'payload summary存储的字段' },
          parent_id: { type: :integer, description: 'closure tree parent_id' },
          code: { type: :string, description: '组织标识' },
          name: { type: :string, description: '组织名称' },
          short_name: { type: :string, description: '组织简称' },
          type: { type: :string, description: 'STI类型，可以是集团，或者在某些时候可能是学校这样的类型' },
          position: { type: :integer, description: '排序' },
          region_area_id: { type: :integer, description: '' },
          province: { type: :string, description: '省' },
          city: { type: :string, description: '市' },
          district: { type: :string, description: '区' },
          tanent_id: { type: :integer, description: '' },
        }
      }
    }
  }
  org_value = FactoryBot.attributes_for(:org)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/orgs' do

    get(summary: 'list orgs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq Org.count
        }
      end
    end
  end
end
