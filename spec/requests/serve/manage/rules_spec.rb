require 'swagger_helper'

RSpec.describe 'serve/manage/rules', type: :request, capture_examples: true, tags: ["serve manage"] do
  rule_ref = {
    type: :object, properties: {
      rule: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          position: { type: :integer, description: '排序' },
          options: { type: :jsonb, description: '配置' }
        }
      }
    }
  }
  rule_value = FactoryBot.attributes_for(:serve_rule)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/rules' do
    get(summary: 'list rules') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_rule_count
        }
      end
    end

    post(summary: 'create rule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule, in: :body, schema: rule_ref
      response(201, description: 'successful') do
        let(:rule) do
          { rule: rule_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq rule_value[:creator_id]
          expect(body['type']).to eq rule_value[:type]
          expect(body['model_flag']).to eq rule_value[:model_flag]
          expect(body['model_payload']).to eq rule_value[:model_payload]
          expect(body['model_payload_summary']).to eq rule_value[:model_payload_summary]
          expect(body['model_detail']).to eq rule_value[:model_detail]
          expect(body['effective_at']).to eq rule_value[:effective_at]
          expect(body['invalid_at']).to eq rule_value[:invalid_at]
          expect(body['name']).to eq rule_value[:name]
          expect(body['state']).not_to be_nil
          expect(body['position']).to eq rule_value[:position]
          expect(body['options']).not_to be_nil
        }
      end
    end
  end

  path '/serve/manage/rules/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show rule') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_rules.first.id }
        it {
          body = JSON.parse(response.body)
          rule = @serve_rules.first
          expect(body['app_id']).to eq rule.app_id
          expect(body['creator_id']).to eq rule.creator_id
          expect(body['type']).to eq rule.type
          expect(body['model_flag']).to eq rule.model_flag
          expect(body['model_payload']).to eq rule.model_payload
          expect(body['model_payload_summary']).to eq rule.model_payload_summary
          expect(body['model_detail']).to eq rule.model_detail
          expect(body['effective_at']).to eq rule.effective_at
          expect(body['invalid_at']).to eq rule.invalid_at
          expect(body['name']).to eq rule.name
          expect(body['state']).to eq rule.state
          expect(body['position']).to eq rule.position
          expect(body['options']).not_to be_nil
        }
      end
    end

    patch(summary: 'update rule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule, in: :body, schema: rule_ref
      response(201, description: 'successful') do
        let(:id) { @serve_rules.first.id }
        let(:rule) do
          { rule: rule_value }
        end
      end
    end

    delete(summary: 'delete rule') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_rules.first.id }
        it {
          expect(Serve::Rule.count).to eq(@serve_rule_count-1)
        }
      end
    end
  end
end
