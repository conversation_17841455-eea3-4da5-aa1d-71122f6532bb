require 'swagger_helper'

RSpec.describe 'serve/manage/ai_message_squares', type: :request, capture_examples: true, tags: ["serve manage"] do
  ai_message_square_ref = {
    type: :object, properties: {
      ai_message_square: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          org_id: { type: :integer, description: '' },
          pack_id: { type: :integer, description: '' },
          rule_id: { type: :integer, description: '' },
          ref_ai_message_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          seq: { type: :string, description: '编号' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          content: { type: :text, description: '内容' },
          option: { type: :jsonb, description: '配置' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  ai_message_square_value = FactoryBot.attributes_for(:serve_ai_message_square)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/ai_message_squares/{id}/clone_to_template' do
    parameter 'id', in: :path, type: :string

    post(summary: 'clone_to_template ai_message_square') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ai_message_square, in: :body, schema: ai_message_square_ref
      response(201, description: 'successful') do
        let(:id) { @serve_ai_message_squares.first.id }
        let(:ai_message_square) do
          { ai_message_square: ai_message_square_value }
        end
      end
    end
  end

  path '/serve/manage/ai_message_squares' do

    get(summary: 'list ai_message_squares') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_ai_message_square_count
        }
      end
    end
  end

  path '/serve/manage/ai_message_squares/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show ai_message_square') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_ai_message_squares.first.id }
        it {
          body = JSON.parse(response.body)
          ai_message_square = @serve_ai_message_squares.first
          expect(body['app_id']).to eq ai_message_square.app_id
          expect(body['creator_id']).to eq ai_message_square.creator_id
          expect(body['org_id']).to eq ai_message_square.org_id
          expect(body['pack_id']).to eq ai_message_square.pack_id
          expect(body['rule_id']).to eq ai_message_square.rule_id
          expect(body['ref_ai_message_id']).to eq ai_message_square.ref_ai_message_id
          expect(body['type']).to eq ai_message_square.type
          expect(body['seq']).to eq ai_message_square.seq
          expect(body['name']).to eq ai_message_square.name
          expect(body['state']).to eq ai_message_square.state
          expect(body['content']).to eq ai_message_square.content
          expect(body['option']).to eq ai_message_square.option
          expect(body['payload']).to eq ai_message_square.payload
        }
      end
    end
  end
end
