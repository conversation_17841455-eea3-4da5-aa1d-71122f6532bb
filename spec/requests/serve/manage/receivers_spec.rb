require 'swagger_helper'

RSpec.describe 'serve/manage/receivers', type: :request, capture_examples: true, tags: ["serve manage"] do
  receiver_ref = {
    type: :object, properties: {
      receiver: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          pack_id: { type: :integer, description: '' },
          state: { type: :string, description: '状态' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  receiver_value = FactoryBot.attributes_for(:serve_receiver)

  before :each do
    @user.add_role :serve_manage
    @pack = @serve_packs.last
  end

  path '/serve/manage/packs/{pack_id}/receivers' do
    parameter 'pack_id', in: :path, type: :string

    get(summary: 'list receivers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:pack_id) { @serve_pack.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_receiver_count
        }
      end
    end

    post(summary: 'create receiver') do
      produces 'application/json'
      consumes 'application/json'
      parameter :receiver, in: :body, schema: receiver_ref
      response(201, description: 'successful') do
        let(:pack_id) { @pack.id }
        let(:receiver) do
          { receiver: receiver_value.merge(user_id: @user.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['pack_id']).to eq @pack.id
          expect(body['state']).to eq receiver_value[:state]
          expect(body['payload']).to eq receiver_value[:payload]
        }
      end
    end
  end

  path '/serve/manage/packs/{pack_id}/receivers/{id}' do
    parameter 'pack_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show receiver') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:pack_id) { @serve_pack.id }
        let(:id) { @serve_receivers.first.id }
        it {
          body = JSON.parse(response.body)
          receiver = @serve_receivers.first
          expect(body['app_id']).to eq receiver.app_id
          expect(body['user_id']).to eq receiver.user_id
          expect(body['pack_id']).to eq receiver.pack_id
          expect(body['state']).to eq receiver.state
          expect(body['payload']).to eq receiver.payload
        }
      end
    end

    patch(summary: 'update receiver') do
      produces 'application/json'
      consumes 'application/json'
      parameter :receiver, in: :body, schema: receiver_ref
      response(201, description: 'successful') do
        let(:pack_id) { @serve_pack.id }
        let(:id) { @serve_receivers.first.id }
        let(:receiver) do
          { receiver: receiver_value }
        end
      end
    end

    delete(summary: 'delete receiver') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:pack_id) { @serve_pack.id }
        let(:id) { @serve_receivers.first.id }
        it {
          expect(Serve::Receiver.count).to eq(@serve_receiver_count-1)
        }
      end
    end
  end
end
