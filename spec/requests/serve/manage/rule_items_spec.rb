require 'swagger_helper'

RSpec.describe 'serve/manage/rule_items', type: :request, capture_examples: true, tags: ["serve manage"] do
  rule_item_ref = {
    type: :object, properties: {
      rule_item: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          rule_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          option: { type: :jsonb, description: '配置' },
          payload: { type: :jsonb, description: '其他字段' }
        }
      }
    }
  }
  rule_item_value = FactoryBot.attributes_for(:serve_rule_item)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/rule_items' do
    get(summary: 'list rule_items') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_rule_item_count
        }
      end
    end

    post(summary: 'create rule_item') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule_item, in: :body, schema: rule_item_ref
      response(201, description: 'successful') do
        let(:rule_item) do
          { rule_item: rule_item_value.merge(rule_id: @serve_rule.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['rule_id']).to eq @serve_rule.id
          expect(body['name']).to eq rule_item_value[:name]
          expect(body['state']).to eq rule_item_value[:state]
          expect(body['option']).to eq rule_item_value[:option]
          expect(body['payload']).to eq rule_item_value[:payload]
        }
      end
    end
  end

  path '/serve/manage/rule_items/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show rule_item') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_rule_items.first.id }
        it {
          body = JSON.parse(response.body)
          rule_item = @serve_rule_items.first
          expect(body['app_id']).to eq rule_item.app_id
          expect(body['rule_id']).to eq rule_item.rule_id
          expect(body['name']).to eq rule_item.name
          expect(body['state']).to eq rule_item.state
          expect(body['option']).to eq rule_item.option
          expect(body['payload']).to eq rule_item.payload
        }
      end
    end

    patch(summary: 'update rule_item') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule_item, in: :body, schema: rule_item_ref
      response(201, description: 'successful') do
        let(:id) { @serve_rule_items.first.id }
        let(:rule_item) do
          { rule_item: rule_item_value }
        end
      end
    end

    delete(summary: 'delete rule_item') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_rule_items.first.id }
        it {
          expect(Serve::RuleItem.count).to eq(@serve_rule_item_count-1)
        }
      end
    end
  end
end
