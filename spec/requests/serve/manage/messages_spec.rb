require 'swagger_helper'

RSpec.describe 'serve/manage/messages', type: :request, capture_examples: true, tags: ["serve manage"] do
  message_ref = {
    type: :object, properties: {
      message: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          notifyable_type: { type: :string, description: '' },
          notifyable_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          seq: { type: :string, description: '编号' },
          type: { type: :string, description: 'STI属性' },
          read_at: { type: :datetime, description: '' },
          is_read: { type: :boolean, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          send_at: { type: :datetime, description: '发送时间' },
          body: { type: :jsonb, description: '发送信息内容' },
          response: { type: :jsonb, description: '发送结果' }
        }
      }
    }
  }
  message_value = FactoryBot.attributes_for(:serve_message)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/messages' do
    get(summary: 'list messages') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_message_count
        }
      end
    end

    post(summary: 'create message') do
      produces 'application/json'
      consumes 'application/json'
      parameter :message, in: :body, schema: message_ref
      response(201, description: 'successful') do
        let(:message) do
          { message: message_value.merge(user_id: @user.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['notifyable_type']).to eq message_value[:notifyable_type]
          expect(body['notifyable_id']).to eq message_value[:notifyable_id]
          expect(body['user_id']).to eq @user.id
          expect(body['seq']).not_to be_nil
          expect(body['type']).to eq message_value[:type]
          expect(body['read_at']).to eq message_value[:read_at]
          expect(body['is_read']).not_to be_nil
          expect(body['name']).to eq message_value[:name]
          expect(body['state']).to eq message_value[:state]
          expect(body['send_at']).to eq message_value[:send_at]
          expect(body['body']).to eq message_value[:body]
          expect(body['response']).to eq message_value[:response]
        }
      end
    end
  end

  path '/serve/manage/messages/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show message') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_messages.first.id }
        it {
          body = JSON.parse(response.body)
          message = @serve_messages.first
          expect(body['app_id']).to eq message.app_id
          expect(body['notifyable_type']).to eq message.notifyable_type
          expect(body['notifyable_id']).to eq message.notifyable_id
          expect(body['user_id']).to eq message.user_id
          expect(body['seq']).to eq message.seq
          expect(body['type']).to eq message.type
          expect(body['read_at']).to eq message.read_at
          expect(body['is_read']).to eq message.is_read
          expect(body['name']).to eq message.name
          expect(body['state']).to eq message.state
          expect(body['send_at']).to eq message.send_at
          expect(body['body']).to eq message.body
          expect(body['response']).to eq message.response
        }
      end
    end

    patch(summary: 'update message') do
      produces 'application/json'
      consumes 'application/json'
      parameter :message, in: :body, schema: message_ref
      response(201, description: 'successful') do
        let(:id) { @serve_messages.first.id }
        let(:message) do
          { message: message_value }
        end
      end
    end

    delete(summary: 'delete message') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_messages.first.id }
        it {
          expect(Serve::Message.count).to eq(@serve_message_count-1)
        }
      end
    end
  end
end
