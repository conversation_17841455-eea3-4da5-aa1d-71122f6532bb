require 'swagger_helper'

RSpec.describe 'serve/manage/rule_groups', type: :request, capture_examples: true, tags: ["serve manage"] do
  rule_group_ref = {
    type: :object, properties: {
      rule_group: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          position: { type: :integer, description: '排序' },
          payload: { type: :jsonb, description: '其他字段' }
        }
      }
    }
  }
  rule_group_value = FactoryBot.attributes_for(:serve_rule_group)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/rule_groups' do
    get(summary: 'list rule_groups') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_rule_group_count
        }
      end
    end

    post(summary: 'create rule_group') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule_group, in: :body, schema: rule_group_ref
      response(201, description: 'successful') do
        let(:rule_group) do
          { rule_group: rule_group_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['submodule_id']).to eq rule_group_value[:submodule_id]
          expect(body['name']).to eq rule_group_value[:name]
          expect(body['state']).to eq rule_group_value[:state]
          expect(body['position']).not_to be_nil
          expect(body['payload']).to eq rule_group_value[:payload]
        }
      end
    end
  end

  path '/serve/manage/rule_groups/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show rule_group') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_rule_groups.first.id }
        it {
          body = JSON.parse(response.body)
          rule_group = @serve_rule_groups.first
          expect(body['app_id']).to eq rule_group.app_id
          expect(body['submodule_id']).to eq rule_group.submodule_id
          expect(body['name']).to eq rule_group.name
          expect(body['state']).to eq rule_group.state
          expect(body['position']).to eq rule_group.position
          expect(body['payload']).to eq rule_group.payload
        }
      end
    end

    patch(summary: 'update rule_group') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule_group, in: :body, schema: rule_group_ref
      response(201, description: 'successful') do
        let(:id) { @serve_rule_groups.first.id }
        let(:rule_group) do
          { rule_group: rule_group_value }
        end
      end
    end

    delete(summary: 'delete rule_group') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_rule_groups.first.id }
        it {
          expect(Serve::RuleGroup.count).to eq(@serve_rule_group_count-1)
        }
      end
    end
  end
end
