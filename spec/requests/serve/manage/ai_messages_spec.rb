require 'swagger_helper'

RSpec.describe 'serve/manage/ai_messages', type: :request, capture_examples: true, tags: ["serve manage"] do
  ai_message_ref = {
    type: :object, properties: {
      ai_message: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          org_id: { type: :integer, description: '' },
          pack_id: { type: :integer, description: '' },
          rule_id: { type: :integer, description: '' },
          ref_ai_message_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          seq: { type: :string, description: '编号' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          content: { type: :text, description: '内容' },
          option: { type: :jsonb, description: '配置' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  ai_message_value = FactoryBot.attributes_for(:serve_ai_message)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/ai_messages' do

    get(summary: 'list ai_messages') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_ai_message_count
        }
      end
    end

    post(summary: 'create ai_message') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ai_message, in: :body, schema: ai_message_ref
      response(201, description: 'successful') do
        let(:ai_message) do
          { ai_message: ai_message_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['creator_id']).to eq @user.id
          expect(body['org_id']).to eq ai_message_value[:org_id]
          expect(body['pack_id']).to eq ai_message_value[:pack_id]
          expect(body['rule_id']).to eq ai_message_value[:rule_id]
          expect(body['ref_ai_message_id']).to eq ai_message_value[:ref_ai_message_id]
          expect(body['type']).to eq ai_message_value[:type]
          expect(body['seq']).not_to be_nil
          expect(body['name']).to eq ai_message_value[:name]
          expect(body['state']).to eq ai_message_value[:state]
          expect(body['content']).to eq ai_message_value[:content]
          expect(body['option']).to eq ai_message_value[:option]
          expect(body['payload']).to eq ai_message_value[:payload]
        }
      end
    end
  end

  path '/serve/manage/ai_messages/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show ai_message') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_ai_messages.first.id }
        it {
          body = JSON.parse(response.body)
          ai_message = @serve_ai_messages.first
          expect(body['app_id']).to eq ai_message.app_id
          expect(body['creator_id']).to eq ai_message.creator_id
          expect(body['org_id']).to eq ai_message.org_id
          expect(body['pack_id']).to eq ai_message.pack_id
          expect(body['rule_id']).to eq ai_message.rule_id
          expect(body['ref_ai_message_id']).to eq ai_message.ref_ai_message_id
          expect(body['type']).to eq ai_message.type
          expect(body['seq']).to eq ai_message.seq
          expect(body['name']).to eq ai_message.name
          expect(body['state']).to eq ai_message.state
          expect(body['content']).to eq ai_message.content
          expect(body['option']).to eq ai_message.option
          expect(body['payload']).to eq ai_message.payload
        }
      end
    end

    patch(summary: 'update ai_message') do
      produces 'application/json'
      consumes 'application/json'
      parameter :ai_message, in: :body, schema: ai_message_ref
      response(201, description: 'successful') do
        let(:id) { @serve_ai_messages.first.id }
        let(:ai_message) do
          { ai_message: ai_message_value }
        end
      end
    end

    delete(summary: 'delete ai_message') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_ai_messages.first.id }
        it {
          expect(Serve::AiMessage.count).to eq(@serve_ai_message_count-1)
        }
      end
    end
  end
end
