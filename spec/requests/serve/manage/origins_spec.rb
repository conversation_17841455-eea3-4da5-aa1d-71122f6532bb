require 'swagger_helper'

RSpec.describe 'serve/manage/origins', type: :request, capture_examples: true, tags: ["serve manage"] do
  origin_ref = {
    type: :object, properties: {
      origin: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          org_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          code: { type: :string, description: '标识' },
          position: { type: :integer, description: '排序' },
          payload: { type: :jsonb, description: '其他字段' },
          submodule_id: { type: :integer, description: '' }
        }
      }
    }
  }
  origin_value = FactoryBot.attributes_for(:serve_origin)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/origins' do
    get(summary: 'list origins') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_origin_count
        }
      end
    end

    post(summary: 'create origin') do
      produces 'application/json'
      consumes 'application/json'
      parameter :origin, in: :body, schema: origin_ref
      response(201, description: 'successful') do
        let(:origin) do
          { origin: origin_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['org_id']).to eq origin_value[:org_id]
          expect(body['model_flag']).to eq origin_value[:model_flag]
          expect(body['model_payload']).to eq origin_value[:model_payload]
          expect(body['model_payload_summary']).to eq origin_value[:model_payload_summary]
          expect(body['model_detail']).to eq origin_value[:model_detail]
          expect(body['type']).to eq origin_value[:type]
          expect(body['name']).to eq origin_value[:name]
          expect(body['state']).to eq origin_value[:state]
          expect(body['code']).to eq origin_value[:code]
          expect(body['position']).to eq origin_value[:position]
          expect(body['payload']).to eq origin_value[:payload]
          expect(body['submodule_id']).to eq origin_value[:submodule_id]
        }
      end
    end
  end

  path '/serve/manage/origins/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show origin') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_origins.first.id }
        it {
          body = JSON.parse(response.body)
          origin = @serve_origins.first
          expect(body['app_id']).to eq origin.app_id
          expect(body['org_id']).to eq origin.org_id
          expect(body['model_flag']).to eq origin.model_flag
          expect(body['model_payload']).to eq origin.model_payload
          expect(body['model_payload_summary']).to eq origin.model_payload_summary
          expect(body['model_detail']).to eq origin.model_detail
          expect(body['type']).to eq origin.type
          expect(body['name']).to eq origin.name
          expect(body['state']).to eq origin.state
          expect(body['code']).to eq origin.code
          expect(body['position']).to eq origin.position
          expect(body['payload']).to eq origin.payload
          expect(body['submodule_id']).to eq origin.submodule_id
        }
      end
    end

    patch(summary: 'update origin') do
      produces 'application/json'
      consumes 'application/json'
      parameter :origin, in: :body, schema: origin_ref
      response(201, description: 'successful') do
        let(:id) { @serve_origins.first.id }
        let(:origin) do
          { origin: origin_value }
        end
      end
    end

    delete(summary: 'delete origin') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_origins.first.id }
        it {
          expect(Serve::Origin.count).to eq(@serve_origin_count-1)
        }
      end
    end
  end
end
