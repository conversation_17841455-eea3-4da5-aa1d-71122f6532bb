require 'swagger_helper'

RSpec.describe 'serve/manage/packs', type: :request, capture_examples: true, tags: ["serve manage"] do
  pack_ref = {
    type: :object, properties: {
      pack: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          rule_id: { type: :integer, description: '' },
          activity_id: { type: :integer, description: '' },
          create_instance_state: { type: :string, description: '创建工作流的状态' },
          create_instance_timestamp: { type: :datetime, description: '创建工作流的操作时间' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          seq: { type: :string, description: '标识' },
          period: { type: :integer, description: '周期' },
          operate_at: { type: :datetime, description: '操作时间' },
          send_at: { type: :datetime, description: '发送时间' },
          payload: { type: :jsonb, description: '其他信息' },
          option: { type: :jsonb, description: '配置信息' },
          position: { type: :integer, description: '排序' }
        }
      }
    }
  }
  pack_value = FactoryBot.attributes_for(:serve_pack)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/packs' do
    get(summary: 'list packs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_pack_count
        }
      end
    end

    post(summary: 'create pack') do
      produces 'application/json'
      consumes 'application/json'
      parameter :pack, in: :body, schema: pack_ref
      response(201, description: 'successful') do
        let(:pack) do
          { pack: pack_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['rule_id']).to eq pack_value[:rule_id]
          expect(body['activity_id']).to eq pack_value[:activity_id]
          expect(body['create_instance_state']).to eq pack_value[:create_instance_state]
          expect(body['create_instance_timestamp']).to eq pack_value[:create_instance_timestamp]
          expect(body['type']).to eq pack_value[:type]
          expect(body['name']).to eq pack_value[:name]
          expect(body['state']).not_to be_nil
          expect(body['seq']).to eq pack_value[:seq]
          expect(body['period']).to eq pack_value[:period]
          expect(body['operate_at']).to eq pack_value[:operate_at]
          expect(body['send_at']).to eq pack_value[:send_at]
          expect(body['payload']).to eq pack_value[:payload]
          expect(body['option']).to eq pack_value[:option]
        }
      end
    end
  end

  path '/serve/manage/packs/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show pack') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_packs.first.id }
        it {
          body = JSON.parse(response.body)
          pack = @serve_packs.first
          expect(body['app_id']).to eq pack.app_id
          expect(body['rule_id']).to eq pack.rule_id
          expect(body['activity_id']).to eq pack.activity_id
          expect(body['create_instance_state']).to eq pack.create_instance_state
          expect(body['create_instance_timestamp']).to eq pack.create_instance_timestamp
          expect(body['type']).to eq pack.type
          expect(body['name']).to eq pack.name
          expect(body['state']).to eq pack.state
          expect(body['seq']).to eq pack.seq
          expect(body['period']).to eq pack.period
          expect(body['operate_at']).to eq pack.operate_at
          expect(body['send_at']).to eq pack.send_at
          expect(body['payload']).to eq pack.payload
          expect(body['option']).to eq pack.option
          expect(body['position']).to eq pack.position
        }
      end
    end

    patch(summary: 'update pack') do
      produces 'application/json'
      consumes 'application/json'
      parameter :pack, in: :body, schema: pack_ref
      response(201, description: 'successful') do
        let(:id) { @serve_packs.first.id }
        let(:pack) do
          { pack: pack_value }
        end
      end
    end

    delete(summary: 'delete pack') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_packs.first.id }
        it {
          expect(Serve::Pack.count).to eq(@serve_pack_count-1)
        }
      end
    end
  end
end
