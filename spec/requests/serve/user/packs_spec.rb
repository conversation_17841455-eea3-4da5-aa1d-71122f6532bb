require 'swagger_helper'

RSpec.describe 'serve/user/packs', type: :request, capture_examples: true, tags: ["serve user"] do
  before :each do
  end

  path '/serve/user/packs/{id}/refresh_contents_by_rule' do
    parameter 'id', in: :path, type: :string

    post(summary: 'refresh_contents_by_rule pack') do
      produces 'application/json'
      consumes 'application/json'
      # parameter :pack, in: :body, schema: pack_ref
      response(201, description: 'successful') do
        let(:id) { @serve_packs.first.id }
        # let(:pack) do
        #   { pack: pack_value }
        # end
      end
    end
  end

  path '/serve/user/packs/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show pack') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_packs.first.id }
        it {
          body = JSON.parse(response.body)
          pack = @serve_packs.first
          expect(body['app_id']).to eq pack.app_id
          expect(body['rule_id']).to eq pack.rule_id
          expect(body['activity_id']).to eq pack.activity_id
          expect(body['create_instance_state']).to eq pack.create_instance_state
          expect(body['create_instance_timestamp']).to eq pack.create_instance_timestamp
          expect(body['type']).to eq pack.type
          expect(body['name']).to eq pack.name
          expect(body['state']).to eq pack.state
          expect(body['seq']).to eq pack.seq
          expect(body['period']).to eq pack.period
          expect(body['operate_at']).to eq pack.operate_at
          expect(body['send_at']).to eq pack.send_at
          expect(body['payload']).to eq pack.payload
          expect(body['option']).to eq pack.option
          expect(body['position']).to eq pack.position
          expect(body['org_id']).to eq pack.org_id
          expect(body['creator_id']).to eq pack.creator_id
          expect(body['rule_record_id']).to eq pack.rule_record_id
          expect(body['source_type']).to eq pack.source_type
          expect(body['source_id']).to eq pack.source_id
          expect(body['flag']).to eq pack.flag
          expect(body['message_type']).to eq pack.message_type
          expect(body['tanent_id']).to eq pack.tanent_id
          expect(body['rule_item_id']).to eq pack.rule_item_id
          expect(body['content']).to eq pack.content
        }
      end
    end
  end
end
