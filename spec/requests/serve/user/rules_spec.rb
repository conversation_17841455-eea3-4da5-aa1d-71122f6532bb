require 'swagger_helper'

RSpec.describe 'serve/user/rules', type: :request, capture_examples: true, tags: ["serve user"] do
  rule_ref = {
    type: :object, properties: {
      rule: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          position: { type: :integer, description: '排序' },
          options: { type: :jsonb, description: '配置' },
          batch_no: { type: :integer, description: '批次' },
          code: { type: :string, description: '编号' },
          latest_send_at: { type: :datetime, description: '最新发送时间' },
          catalog_id: { type: :integer, description: '' },
          rule_conf: { type: :jsonb, description: '规则配置' },
          rule_record_type: { type: :string, description: 'rule_record 的 STI 类型' },
          message_type: { type: :string, description: '消息类型' },
          rule_group_id: { type: :integer, description: '' },
          content: { type: :jsonb, description: '内容' },
          description: { type: :text, description: '工具描述' },
        }
      }
    }
  }
  rule_value = FactoryBot.attributes_for(:serve_rule)

  before :each do
    @serve_rule_count = 5
    @serve_rules = FactoryBot.create_list(:serve_rule, @serve_rule_count)
  end

  path '/serve/user/rules/{id}/generate_content_by_template_prompt' do
    parameter 'id', in: :path, type: :string

    post(summary: 'generate_content_by_template_prompt rule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :rule, in: :body, schema: rule_ref
      response(201, description: 'successful') do
        let(:id) { @serve_rules.first.id }
        let(:rule) do
          { rule: rule_value }
        end
      end
    end
  end

  path '/serve/user/rules/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show rule') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_rules.first.id }
        it {
          body = JSON.parse(response.body)
          rule = @serve_rules.first
          expect(body['app_id']).to eq rule.app_id
          expect(body['creator_id']).to eq rule.creator_id
          expect(body['type']).to eq rule.type
          expect(body['model_flag']).to eq rule.model_flag
          expect(body['model_payload']).to eq rule.model_payload
          expect(body['model_payload_summary']).to eq rule.model_payload_summary
          expect(body['model_detail']).to eq rule.model_detail
          expect(body['effective_at']).to eq rule.effective_at
          expect(body['invalid_at']).to eq rule.invalid_at
          expect(body['name']).to eq rule.name
          expect(body['state']).to eq rule.state
          expect(body['position']).to eq rule.position
          expect(body['options']).to eq rule.options
          expect(body['batch_no']).to eq rule.batch_no
          expect(body['code']).to eq rule.code
          expect(body['latest_send_at']).to eq rule.latest_send_at
          expect(body['catalog_id']).to eq rule.catalog_id
          expect(body['rule_conf']).to eq rule.rule_conf
          expect(body['rule_record_type']).to eq rule.rule_record_type
          expect(body['message_type']).to eq rule.message_type
          expect(body['rule_group_id']).to eq rule.rule_group_id
          expect(body['content']).to eq rule.content
          expect(body['description']).to eq rule.description
        }
      end
    end
  end
end
