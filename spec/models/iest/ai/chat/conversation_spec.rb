require 'rails_helper'

RSpec.describe Iest::Ai::Chat::Conversation, type: :model do
  describe 'create message' do
    before(:each) do
      @conversation = @iest_ai_chat_conversations.last

      allow(Iest::Ai::OpenaiService).to receive(:simple_request).and_return({
        'send_at' => '2022-09-30 08:00:00',
        'user_name' => [@user.name]
      })

      allow(Iest::Ai::OpenaiService).to receive(:completions).and_return({
        'send_at' => '2022-09-30 08:00:00',
        'user_name' => [@user.name],
        'intent' => @create_serve_pack_intent.name
      }.to_json)

      @message_1 = @conversation.messages.create!(
        content: '给纪委一把手发一篇国庆廉政提醒',
        type: 'Iest::Ai::Chat::Messages::User',
      )

      @message_2 = @conversation.messages.create!(
        previous_content: '给纪委一把手发一篇国庆廉政提醒',
        type: 'Iest::Ai::Chat::Messages::System',
      )
    end

    it 'should create message' do
      expect(@message_2.result_type).to eq('create_serve_pack_serve_activity_ids_empty')
      expect(@message_2.mentions.count).to eq(1)
      expect(@message_2.content).not_to be_nil

      mention = @message_2.mentions.first
      expect(mention.type).to eq('Iest::Ai::Chat::Mentions::ServePack')
      expect(mention.name).not_to be_nil
      expect(mention.payload).not_to be_nil
      expect(mention.payload.dig('send_at')).not_to be_nil
      expect(mention.payload.dig('payload', 'user_ids')).to eq([@user.id])
      expect(mention.mention_versions.count).to eq(1)

      expect(mention.selectable_intents.count).to eq(3)
      expect(mention.selectable_intents.pluck(:id).sort).to eq(
        [
          @create_serve_pack_intent,
          @intent_message_user,
          @intent_message_article
        ].map(&:id).sort
      )

      expect {
        mention.selectable_intents.prompt
      }.not_to raise_error
    end
  end
end
