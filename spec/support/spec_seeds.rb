RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    @modules = example.metadata[:tags]&.first&.split(' ') || []
    @app = create(:app)
    @user = create(:user, app: @app)
    user_account_info = {
      account_type: 'User',
      account: @user.account
    }
    allow(User).to receive(:auth!).and_return(user_account_info)

    allow(Iest::Ai::OpenaiService).to receive(:completions).and_return({})
    allow(Iest::Ai::OpenaiService).to receive(:simple_request).and_return({})
    allow(Iest::Ai::OpenaiService).to receive(:embeddings).and_return([[]])

    @org = @app.orgs.create(name: 'hanzhou')
    @user.org_ids = [@org.id]
    @submodule = Serve::Submodule.create app: @app, name: 'submodule'
    @catalog = Serve::Catalog.create app: @app, name: 'catalog'

    create_list :serve_rule_group, 2, app: @app
    @serve_rule_groups = Serve::RuleGroup.all
    @serve_rule_group = @serve_rule_groups.first
    @serve_rule_group_count = @serve_rule_groups.count

    create_list :serve_rule, 2, app: @app, creator: @user, org_ids: [@org.id]
    @serve_rules = Serve::Rule.all
    @serve_rule = @serve_rules.first
    @serve_rule_count = @serve_rules.count

    create_list :serve_rule_item, 2, rule: @serve_rule, app: @app
    @serve_rule_items = Serve::RuleItem.all
    @serve_rule_item = @serve_rule_items.first
    @serve_rule_item_count = @serve_rule_items.count

    create_list :serve_pack, 2, app: @app, rule: @serve_rule, org: @org, creator: @user
    @serve_packs = Serve::Pack.all
    @serve_pack = @serve_packs.first
    @serve_pack_count = @serve_packs.count

    create_list :serve_origin, 2, app: @app
    @serve_origins = Serve::Origin.all
    @serve_origin = @serve_origins.first
    @serve_origin_count = @serve_origins.count

    create_list :serve_message, 2, app: @app, user: @user, pack: @serve_pack, sender: @user
    @serve_messages = Serve::Message.all
    @serve_message = @serve_messages.first
    @serve_message_count = @serve_messages.count

    create_list :serve_message_template, 2, app: @app, rule: @serve_rule
    @serve_message_templates = Serve::MessageTemplate.all
    @serve_message_template = @serve_message_templates.first
    @serve_message_template_count = @serve_message_templates.count

    create_list :serve_ai_message_square, 2, app: @app, creator: @user, rule: @serve_rule
    @serve_ai_message_squares = Serve::AiMessageSquare.all
    @serve_ai_message_square = @serve_ai_message_squares.first
    @serve_ai_message_square_count = @serve_ai_message_squares.count

    create_list :serve_ai_message_template, 2, app: @app, creator: @user
    @serve_ai_message_templates = Serve::AiMessageTemplate.all
    @serve_ai_message_template = @serve_ai_message_templates.first
    @serve_ai_message_template_count = @serve_ai_message_templates.count

    @serve_ai_messages = Serve::AiMessage.all
    @serve_ai_message = @serve_ai_messages.first
    @serve_ai_message_count = @serve_ai_messages.count

    create_list :serve_receiver, 1, app: @app, user: @user, pack: @serve_pack
    @serve_receivers = Serve::Receiver.all
    @serve_receiver = @serve_receivers.first
    @serve_receiver_count = @serve_receivers.count

    # @iest_ai_chat_conversations = create_list(:iest_ai_chat_conversation, 3, app: @app)
    # @iest_ai_chat_conversation = @iest_ai_chat_conversations.first
    # @iest_ai_chat_conversation_count = Iest::Ai::Chat::Conversation.count

    # @iest_ai_chat_messages = create_list(:iest_ai_chat_message, 3, conversation: @iest_ai_chat_conversation)
    # @iest_ai_chat_message = @iest_ai_chat_messages.first
    # @iest_ai_chat_message_count = Iest::Ai::Chat::Message.count

    # @iest_ai_chat_mentions = create_list(:iest_ai_chat_mention, 3, conversation: @iest_ai_chat_conversation, app: @app)
    # @iest_ai_chat_mention = @iest_ai_chat_mentions.first
    # @iest_ai_chat_mention_count = Iest::Ai::Chat::Mention.count

    # @search_user_intent = create(:iest_ai_chat_intent, name: '找用户', app: @app, type: 'Iest::Ai::Chat::Intents::SearchUser')
    # @search_serve_activity_intent = create(:iest_ai_chat_intent, name: '找文章', app: @app, type: 'Iest::Ai::Chat::Intents::SearchServeActivity')
    # @create_serve_pack_intent = create(:iest_ai_chat_intent, name: '发消息', app: @app, type: 'Iest::Ai::Chat::Intents::CreateServePack')

    # # sub intents
    # @intent_message_user = Iest::Ai::Chat::Intent.find_or_create_by!(app: App.first, name: '找消息接收用户')
    # @intent_message_user.update!(type: 'Iest::Ai::Chat::Intents::SearchUser', parent: @create_serve_pack_intent)
    # @intent_message_article = Iest::Ai::Chat::Intent.find_or_create_by!(app: App.first, name: '找消息的文章')
    # @intent_message_article.update!(type: 'Iest::Ai::Chat::Intents::CreateServePack', parent: @create_serve_pack_intent)

    # @intent_message_user.intent_keywords.find_or_create_by!(name: 'user_name', content_type: :string, desc: '消息接收人员姓名。返回值为数组，数组元素为字符串。在语句中没有明确指出时，返回空数组。')
    # @intent_message_user.intent_keywords.find_or_create_by!(name: 'user_department', content_type: :array, desc: '消息接收人员部门。返回值为数组，数组元素为字符串。需要是中国政府具体部门，在语句中没有明确指出时，返回空数组。对于“各部门”、“各单位”等描述，返回空数组。')
    # @intent_message_user.intent_keywords.find_or_create_by!(name: 'user_tags', content_type: :array, desc: '消息接收人员标签。返回值为数组，数组元素为字符串。可选值有：[{{res_tags}}]，返回值必需在可选值中，在语句中没有明确指出时，返回空数组。')

    # @intent_message_article.intent_keywords.find_or_create_by!(name: 'article_content', content_type: :string, desc: '发送的文章内容一句话概述。')
    # @intent_message_article.intent_keywords.find_or_create_by!(name: 'article_content_type', content_type: :array, desc: '发送文章内容的呈现形式数组。返回值为数组，数组元素为字符串。可选值有：[{{content_type_tags}}]，返回值必需在可选值中。在语句中没有明确指出时，返回空数组。')
  end
end
