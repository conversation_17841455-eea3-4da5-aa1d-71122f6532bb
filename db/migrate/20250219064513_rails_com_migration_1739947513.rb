class RailsComMigration1739947513 < ActiveRecord::Migration[7.1]
  def change
    add_reference :org_members, :tanent
    add_reference :member_requests, :tanent
    add_reference :member_requests, :member
    add_column :member_requests, :type, :string, comment: "STI"
    add_column :memberships, :duty_rank, :string
    add_column :memberships, :priority, :boolean, comment: "是否主要岗位"
    add_column :bpm_workflows, :conf, :jsonb, comment: "其他配置"
    create_table :dingtalk_work_notifications do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.datetime :read_at
      t.boolean :is_read
      t.string :oauth_app_id, comment: "服务名称的标识，对应client的code"
      t.string :openid, comment: "接收者的openid"
      t.string :msg_type, comment: "消息类型: text/link/markdown/action_card/oa"
      t.jsonb :content, comment: "消息内容"
      t.jsonb :response, comment: "发送结果"
      t.string :at_mobiles
      t.string :at_user_ids
      t.boolean :at_all, comment: "是否@所有人"
      t.string :task_id, comment: "工作通知任务ID"
      t.jsonb :progress, comment: "发送进度"
      t.jsonb :send_result, comment: "发送结果详情"
      t.timestamps
    end
    create_table :dingtalk_ding_messages do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.string :oauth_app_id, comment: "服务名称的标识，对应client的code"
      t.jsonb :creator, comment: "创建者信息"
      t.jsonb :dingBody, comment: "发送内容"
      t.jsonb :receivers, comment: "接收者信息(数组对象)"
      t.jsonb :source, comment: "im会话"
      t.boolean :sendToIm, comment: "是否发送到IM会话"
      t.string :scene, comment: "是否发送到IM会话"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    add_column :dingtalk_clients, :company_corp_id, :string, comment: "钉钉企业corp_id"
    add_column :serve_rules, :description, :text, comment: "工具描述"
    add_reference :orgs, :tanent
    add_column :chat_messages, :meta, :jsonb, comment: "发送的内容"
    add_column :chat_mention_versions, :state, :string
    add_reference :members, :tanent
    add_column :members, :effective_at, :date, comment: "生效时间"
    add_column :members, :invalid_at, :date, comment: "失效时间"
  end
end
