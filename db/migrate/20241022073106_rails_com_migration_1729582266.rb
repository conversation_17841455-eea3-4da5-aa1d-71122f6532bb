class RailsComMigration1729582266 < ActiveRecord::Migration[7.1]
  def change
    add_reference :serve_packs, :rule_record
    add_column :schedule_rule_records, :schedule_next_time, :datetime, comment: "下个周期时间"
    add_column :schedule_rule_records, :schedule_offset_at, :datetime, comment: "实际执行时间（考虑时间偏移）"
    create_table :schedule_rules do |t|
      t.references :app
      t.references :creator
      t.jsonb :rule_conf, comment: "规则配置"
      t.string :rule_record_type, comment: "rule_record 的 STI 类型"
      t.string :uuid, comment: "规则唯一标识"
      t.string :name, comment: "规则名称"
      t.timestamps
    end
  end
end
