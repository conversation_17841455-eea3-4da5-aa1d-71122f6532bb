class RailsComMigration1742465864 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_message_templates do |t|
      t.references :rule
      t.references :rule_item
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.jsonb :payload, comment: "其他数据"
      t.jsonb :option, comment: "配置"
      t.timestamps
    end
    add_column :serve_messages, :payload, :jsonb, comment: "其他内容"
    create_table :bot_paperwork_results do |t|
      t.references :paperwork
      t.string :name, comment: "结果名称"
      t.string :raw, comment: "原文"
      t.jsonb :payload, comment: "结果"
      t.timestamps
    end
    create_table :bot_intent_relations do |t|
      t.references :agent
      t.references :intent
      t.timestamps
    end
    create_table :bot_review_results do |t|
      t.references :document_source, polymorphic: true
      t.references :review_rule
      t.string :name, comment: "结果名称"
      t.text :raw, comment: "原文"
      t.integer :score, comment: "分数"
      t.text :reason, comment: "原因"
      t.text :suggest, comment: "建议"
      t.string :level
      t.jsonb :meta, comment: "额外信息"
      t.timestamps
    end
    create_table :bot_messages do |t|
      t.references :app
      t.references :conversation
      t.string :role, comment: "发送对象"
      t.jsonb :meta, comment: "消息发送的内容"
      t.timestamps
    end
    create_table :bot_conversations do |t|
      t.references :app
      t.references :agent
      t.references :user
      t.string :name, comment: "名称"
      t.timestamps
    end
    create_table :bot_artifacts do |t|
      t.references :conversation
      t.references :source, polymorphic: true
      t.string :type, comment: "STI属性"
      t.string :intent_name, comment: "根据名称"
      t.string :tool_cname, comment: "根据模型名称"
      t.string :tool_function, comment: "根据function名称"
      t.jsonb :tool_conf, comment: "参数配置"
      t.jsonb :function_params, comment: "函数调用信息，转换前"
      t.jsonb :meta, comment: "参数内容，自定义的方式"
      t.jsonb :info, comment: "message返回的信息"
      t.timestamps
    end
    create_table :bot_review_rules do |t|
      t.references :review_source, polymorphic: true
      t.string :name, comment: "名称"
      t.text :content, comment: "内容"
      t.text :scoring_criteria, comment: "评分标准"
      t.boolean :active, comment: "是否启用"
      t.timestamps
    end
    create_table :bot_reviewers do |t|
      t.references :app
      t.text :review_instructions, comment: "审查介绍"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :description, comment: "描述"
      t.jsonb :icon, comment: "图标"
      t.timestamps
    end
    create_table :bot_review_documents do |t|
      t.references :app
      t.references :user
      t.references :reviewer
      t.jsonb :file, comment: "文件或内容"
      t.string :name, comment: "名称"
      t.string :review_state
      t.timestamps
    end
    create_table :bot_reports do |t|
      t.references :app
      t.references :user
      t.references :report_template
      t.string :name, comment: "名称"
      t.string :title, comment: "标题"
      t.text :content, comment: "内容"
      t.jsonb :variables, comment: "变量"
      t.string :review_state
      t.timestamps
    end
    create_table :bot_report_templates do |t|
      t.references :app
      t.text :review_instructions, comment: "审查介绍"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :title, comment: "标题"
      t.text :content, comment: "内容"
      t.text :instructions, comment: "介绍"
      t.text :prompt, comment: "提示词"
      t.jsonb :conf, comment: "变量配置"
      t.jsonb :icon, comment: "图标"
      t.timestamps
    end
    create_table :bot_paperworks do |t|
      t.references :app
      t.references :user
      t.string :name, comment: "名称"
      t.datetime :operate_at, comment: "操作时间"
      t.string :state
      t.jsonb :attachment, comment: "附件结构，单一文件"
      t.jsonb :response, comment: "响应"
      t.text :prompt_text, comment: "提示词，由外部传入"
      t.timestamps
    end
    create_table :bot_meetings do |t|
      t.references :app
      t.references :user
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :seq, comment: "编号"
      t.string :name, comment: "会议名称"
      t.string :state
      t.string :meeting_time, comment: "会议时间"
      t.text :background, comment: "会议背景"
      t.string :topic, comment: "会议主题"
      t.text :summary, comment: "会议纪要"
      t.string :participants, comment: "与会人员"
      t.jsonb :audio, comment: "会议录音"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
    create_table :bot_intents do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "工具名称"
      t.text :description, comment: "工具描述"
      t.string :tool_cname, comment: "工具类的名称"
      t.jsonb :tool_conf, comment: "工具配置"
      t.string :llm_model_key, comment: "LlmFactory默认的大模型"
      t.timestamps
    end
    create_table :bot_agents do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "工具名称"
      t.text :description, comment: "工具描述"
      t.text :instructions, comment: "工具介绍"
      t.string :llm_model_key, comment: "LlmFactory默认的大模型"
      t.integer :max_history_messages, comment: "最大历史消息数"
      t.timestamps
    end
  end
end
