class RailsComMigration1731571282 < ActiveRecord::Migration[7.1]
  def change
    create_table :org_relations do |t|
      t.references :source, polymorphic: true
      t.references :org
      t.string :mode
      t.timestamps
    end
    add_column :bpm_workflows, :enable_level, :boolean, comment: "是否启用 instance 优先级"
    add_column :bpm_workflows, :level_options, :jsonb, comment: "优先级配置"
    add_column :bpm_instances, :level, :string, comment: "流程级别"
    add_column :chat_messages, :attachment, :jsonb, comment: "附件"
    add_column :chat_mentions, :ai_response, :jsonb, comment: "当前 AI 返回的数据, 不返回前端"
    remove_column :chat_mentions, :self_intent, :string, comment: "(弃用)意图标识，用于归集相同意图的消息下的 mentions"
    add_column :chat_mention_versions, :ai_response, :jsonb, comment: "当前 AI 返回的数据, 不返回前端"
  end
end
