class RailsComMigration1729500752 < ActiveRecord::Migration[7.1]
  def change
    add_column :serve_rules, :rule_conf, :jsonb, comment: "规则配置"
    add_column :serve_rules, :rule_record_type, :string, comment: "rule_record 的 STI 类型"
    create_table :schedule_rule_records do |t|
      t.references :rule, polymorphic: true
      t.string :type, comment: "STI属性"
      t.datetime :schedule_occurred_at, comment: "发生时间"
      t.string :schedule_uuid, comment: "规则schedule val小项的uuid"
      t.jsonb :schedule_data, comment: "存储具体规则的内容"
      t.timestamps
    end
  end
end
