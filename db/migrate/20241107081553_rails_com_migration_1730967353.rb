class RailsComMigration1730967353 < ActiveRecord::Migration[7.1]
  def change
    create_table :iest_paperworks do |t|
      t.string :name, comment: "名称"
      t.datetime :operate_at, comment: "操作时间"
      t.string :state, comment: "状态"
      t.jsonb :attachment, comment: "附件"
      t.jsonb :response, comment: "响应"
      t.references :app
      t.references :user
      t.references :org

      t.timestamps
    end
    create_table :iest_paperwork_results do |t|
      t.references :paperwork
      t.string :name, comment: "结果名称"
      t.string :raw, comment: "原文"
      t.jsonb :payload, comment: "结果"
      t.timestamps
    end
  end
end
