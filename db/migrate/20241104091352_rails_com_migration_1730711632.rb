class RailsComMigration1730711632 < ActiveRecord::Migration[7.1]
  def change
    create_table :chat_relate_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "chat_relate_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "chat_relate_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "chat_relate_actions_uk_action_target_user"
    end
    create_table :chat_intent_keywords do |t|
      t.references :intent
      t.string :name, comment: "关键词名称"
      t.string :desc, comment: "关键词描述"
      t.string :content_type, comment: "关键词类型"
      t.timestamps
    end
    create_table :chat_intent_hierarchies do |t|
      t.integer :ancestor_id
      t.integer :descendant_id
      t.integer :generations
      t.datetime :created_at
      t.datetime :updated_at
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "chat/intent_anc_desc_idx"
    end
    create_table :chat_intents do |t|
      t.references :app
      t.references :tanent
      t.string :type, comment: "STI属性"
      t.integer :parent_id, comment: "closure tree parent_id"
      t.string :name, comment: "意图名称"
      t.string :desc, comment: "意图描述"
      t.string :prompt_summary, comment: "提示词开场白"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :chat_messages do |t|
      t.references :conversation
      t.string :type, comment: "STI属性"
      t.text :previous_content, comment: "前一句话的内容"
      t.text :content, comment: "内容"
      t.string :result_type, comment: "处理结果标识"
      t.jsonb :suggestions, comment: "提示建议"
      t.timestamps
    end
    create_table :chat_mentions do |t|
      t.references :mentionable, polymorphic: true
      t.references :conversation
      t.references :app
      t.references :creator_intent
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state
      t.jsonb :payload, comment: "数据"
      t.string :self_intent, comment: "(弃用)意图标识，用于归集相同意图的消息下的 mentions"
      t.timestamps
    end
    create_table :chat_mention_versions do |t|
      t.references :mention
      t.references :message
      t.references :conversation
      t.jsonb :payload, comment: "数据"
      t.timestamps
    end
    create_table :chat_conversations do |t|
      t.references :app
      t.references :tanent
      t.references :user
      t.references :current_intent
      t.string :name, comment: "对话名称"
      t.timestamps
    end
  end
end
