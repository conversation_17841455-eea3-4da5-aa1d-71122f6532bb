class RailsComMigration1728999829 < ActiveRecord::Migration[7.1]
  def change
    add_column :serve_rules, :latest_send_at, :datetime, comment: "最新发送时间"
    add_reference :serve_packs, :org
    add_column :serve_origins, :mode, :string, comment: "分类"
    create_table :serve_messages do |t|
      t.references :app
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.string :type, comment: "STI属性"
      t.datetime :read_at
      t.boolean :is_read
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.datetime :send_at, comment: "发送时间"
      t.jsonb :body, comment: "发送信息内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    add_reference :serve_activities, :origin
  end
end
