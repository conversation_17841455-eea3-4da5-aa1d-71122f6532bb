class RailsComMigration1729478086 < ActiveRecord::Migration[7.1]
  def change
    create_table :iest_ai_chat_intent_hierarchies do |t|
      t.integer :ancestor_id
      t.integer :descendant_id
      t.integer :generations
      t.datetime :created_at
      t.datetime :updated_at
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "iest/ai/chat/intent_anc_desc_idx"
    end
    add_column :iest_ai_chat_intents, :parent_id, :integer, comment: "closure tree parent_id"
    add_column :iest_ai_chat_intents, :position, :integer, comment: "排序"
  end
end
