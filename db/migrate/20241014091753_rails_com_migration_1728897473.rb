class RailsComMigration1728897473 < ActiveRecord::Migration[7.1]
  def change
    create_table :serve_origins do |t|
      t.references :app
      t.references :org
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :code, comment: "标识"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :serve_content_type_tags do |t|
      t.references :app
      t.string :name, comment: "名称"
      t.timestamps
    end
    create_table :serve_rules do |t|
      t.references :app
      t.references :creator
      t.string :type, comment: "STI属性"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :name, comment: "名称"
      t.string :state
      t.integer :position, comment: "排序"
      t.jsonb :options, comment: "配置"
      t.timestamps
    end
    create_table :serve_packs do |t|
      t.references :app
      t.references :rule
      t.references :activity
      t.string :create_instance_state, comment: "创建工作流的状态"
      t.datetime :create_instance_timestamp, comment: "创建工作流的操作时间"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state
      t.string :seq, comment: "标识"
      t.integer :period, comment: "周期"
      t.datetime :operate_at, comment: "操作时间"
      t.datetime :send_at, comment: "发送时间"
      t.jsonb :payload, comment: "其他信息"
      t.jsonb :option, comment: "配置信息"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    add_reference :orgs, :region_area
    add_column :orgs, :province, :string, comment: "省"
    add_column :orgs, :city, :string, comment: "市"
    add_column :orgs, :district, :string, comment: "区"
    create_table :region_area_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "region_area_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "region_area_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "region_area_actions_uk_action_target_user"
    end
    create_table :region_areas do |t|
      t.string :ancestry, comment: "树形结构"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据"
      t.string :type, comment: "STI"
      t.string :code, comment: "代码"
      t.string :name, comment: "名称"
      t.string :short_name, comment: "简称"
      t.string :name_en, comment: "拼音简称"
      t.string :name_abbr, comment: "拼音缩写"
      t.timestamps
    end
  end
end
