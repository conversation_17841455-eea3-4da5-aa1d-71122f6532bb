# mod = Mod.where(key: '').first_or_create!(name: '')
# Role.find_or_create_by!(name: '').update!(label: '', mod: mod)
@i = ::Chat::Intent.find_or_create_by!(app: App.first, name: '找用户')
# .update!(type: 'Iest::Ai::Chat::Intents::SearchUser')
@i2 = ::Chat::Intent.find_or_create_by!(app: App.first, name: '找文章')
# .update!(type: 'Iest::Ai::Chat::Intents::SearchServeActivity')
@i.delete
@i2.delete
@intent_message = ::Chat::Intent.find_or_create_by!(app: App.first, name: '发消息')
@intent_message.update!(type: 'Iest::Ai::Chat::Intents::CreateServePack')
@intent_message_user = ::Chat::Intent.find_or_create_by!(app: App.first, name: '找消息接收用户')
@intent_message_user.update!(type: 'Iest::Ai::Chat::Intents::SearchUser', parent: @intent_message)
@intent_message_article = ::Chat::Intent.find_or_create_by!(app: App.first, name: '找消息的文章')
@intent_message_article.update!(type: 'Iest::Ai::Chat::Intents::CreateServePack', parent: @intent_message)

@intent_message_expand_str = ::Chat::Intent.find_or_create_by!(app: App.first, name: '发消息生成自定义内容')
@intent_message_expand_str.update!(type: 'Chat::Intents::ExpandStr', parent: @intent_message)

@intent_message_user.intent_keywords.find_or_create_by!(name: 'user_name', content_type: :string, desc: '消息接收人员姓名。返回值为数组，数组元素为字符串。在语句中没有明确指出时，返回空数组。')
@intent_message_user.intent_keywords.find_or_create_by!(name: 'user_department', content_type: :array, desc: '消息接收人员部门。返回值为数组，数组元素为字符串。需要是中国政府具体部门，在语句中没有明确指出时，返回空数组。对于“各部门”、“各单位”等描述，返回空数组。')
@intent_message_user.intent_keywords.find_or_create_by!(name: 'user_tags', content_type: :array, desc: '消息接收人员标签。返回值为数组，数组元素为字符串。可选值有：[{{res_tags}}]，返回值必需在可选值中，在语句中没有明确指出时，返回空数组。')

@intent_message_article.intent_keywords.find_or_create_by!(name: 'article_content', content_type: :string, desc: '发送的文章内容一句话概述。')
@intent_message_article.intent_keywords.find_or_create_by!(name: 'article_content_type', content_type: :array, desc: '发送文章内容的呈现形式数组。返回值为数组，数组元素为字符串。可选值有：[{{content_type_tags}}]，返回值必需在可选值中。在语句中没有明确指出时，返回空数组。')

# @intent_search_bpm_instance = ::Chat::Intent.find_or_create_by!(app: App.first, name: '找待办')
# @intent_search_bpm_instance.update!(type: 'Iest::Ai::Chat::Intents::SearchBpmInstance', desc: '搜索流程待办、流程抄送、流程已处理、流程未读、流程我创建的。')

@intent_stat_string = ::Chat::Intent.find_or_create_by!(app: App.first, name: '做统计')
@intent_stat_string.update!(type: 'Iest::Ai::Chat::Intents::StatStr', desc: '对统计任务、配置情况、数据分布进行统计表识别，返回简单的统计结果。')

@intent_paperwork = ::Chat::Intent.find_or_create_by!(app: App.first, name: '文档审查')
@intent_paperwork.update!(type: 'Iest::Intents::CreatePaperwork', desc: '对给定的文档文件进行审查，返回审查结果。')
